#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the modern Arduino GUI
اختبار الواجهة العصرية لـ Arduino
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import random

class MockArduinoController:
    """Mock Arduino controller for testing the GUI without hardware"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        
        # Mock data
        self.pwm_values = [128, 64, 192]  # Some initial values
        self.is_connected = False
        
        # Create simple test interface
        self.create_test_interface()
        
        # Start demo animations
        self.start_demo_animations()
    
    def setup_window(self):
        """Setup test window"""
        self.root.title("🚀 Modern Arduino GUI - Demo Test")
        self.root.geometry("800x600")
        self.root.configure(bg='#0a0a0a')
        
        # Modern styling
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Configure modern styles
        self.style.configure('Modern.TFrame',
                           background='#141414',
                           relief='flat',
                           borderwidth=1)
        
        self.style.configure('Modern.TButton',
                           background='#6366f1',
                           foreground='#ffffff',
                           borderwidth=0,
                           focuscolor='none',
                           font=('Segoe UI', 10, 'bold'),
                           padding=(20, 10))
        
        self.style.map('Modern.TButton',
                      background=[('active', '#8b5cf6'),
                                ('pressed', '#4f46e5')])
    
    def create_test_interface(self):
        """Create test interface"""
        main_frame = ttk.Frame(self.root, style='Modern.TFrame', padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(main_frame, 
                              text="🚀 Modern Arduino GUI - Enhanced Features Demo",
                              font=('Segoe UI', 18, 'bold'),
                              fg='#06b6d4',
                              bg='#141414')
        title_label.pack(pady=(0, 30))
        
        # Features demo frame
        features_frame = ttk.Frame(main_frame, style='Modern.TFrame')
        features_frame.pack(fill=tk.BOTH, expand=True)
        
        # PWM Demo Section
        self.create_pwm_demo(features_frame)
        
        # Connection Demo Section
        self.create_connection_demo(features_frame)
        
        # Animation Demo Section
        self.create_animation_demo(features_frame)
    
    def create_pwm_demo(self, parent):
        """Create PWM demonstration"""
        pwm_frame = tk.LabelFrame(parent, text="🎨 PWM Color Visualization Demo",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg='#10b981', bg='#141414',
                                 padx=20, pady=15)
        pwm_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Create color circles
        self.color_canvases = []
        colors = ["#ef4444", "#3b82f6", "#10b981"]  # Red, Blue, Green
        names = ["Red Channel", "Blue Channel", "Green Channel"]
        
        for i, (color, name) in enumerate(zip(colors, names)):
            channel_frame = tk.Frame(pwm_frame, bg='#141414')
            channel_frame.pack(side=tk.LEFT, padx=20, pady=10)
            
            # Color circle
            canvas = tk.Canvas(channel_frame, width=100, height=100,
                             highlightthickness=0, bg='#141414')
            canvas.pack()
            self.color_canvases.append(canvas)
            
            # Label
            label = tk.Label(channel_frame, text=name,
                           font=('Segoe UI', 10, 'bold'),
                           fg='#e8e8e8', bg='#141414')
            label.pack(pady=(10, 0))
            
            # Value label
            value_label = tk.Label(channel_frame, text=f"{self.pwm_values[i]} (50%)",
                                 font=('Segoe UI', 9),
                                 fg='#9ca3af', bg='#141414')
            value_label.pack()
            
            # Initial draw
            self.draw_gradient_circle(canvas, color, self.pwm_values[i])
    
    def create_connection_demo(self, parent):
        """Create connection status demo"""
        conn_frame = tk.LabelFrame(parent, text="🔌 Connection Status Demo",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg='#06b6d4', bg='#141414',
                                  padx=20, pady=15)
        conn_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Connection indicator
        indicator_frame = tk.Frame(conn_frame, bg='#141414')
        indicator_frame.pack(side=tk.LEFT, padx=20)
        
        self.connection_canvas = tk.Canvas(indicator_frame, width=60, height=60,
                                         highlightthickness=0, bg='#141414')
        self.connection_canvas.pack()
        
        # Status label
        self.status_label = tk.Label(indicator_frame, text="Disconnected",
                                   font=('Segoe UI', 11, 'bold'),
                                   fg='#ef4444', bg='#141414')
        self.status_label.pack(pady=(10, 0))
        
        # Connect button
        self.connect_btn = ttk.Button(conn_frame, text="🔗 Connect",
                                     style='Modern.TButton',
                                     command=self.toggle_connection)
        self.connect_btn.pack(side=tk.RIGHT, padx=20)
        
        # Initial state
        self.update_connection_indicator()
    
    def create_animation_demo(self, parent):
        """Create animation demo"""
        anim_frame = tk.LabelFrame(parent, text="✨ Animation & Threading Demo",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg='#8b5cf6', bg='#141414',
                                  padx=20, pady=15)
        anim_frame.pack(fill=tk.X)
        
        # Pulse animation canvas
        self.pulse_canvas = tk.Canvas(anim_frame, width=80, height=80,
                                    highlightthickness=0, bg='#141414')
        self.pulse_canvas.pack(side=tk.LEFT, padx=20)
        
        # Info label
        info_label = tk.Label(anim_frame, 
                            text="• Non-blocking threading for serial communication\n"
                                 "• Smooth animations and visual feedback\n"
                                 "• Responsive UI that never freezes\n"
                                 "• Modern gradient effects and styling",
                            font=('Segoe UI', 10),
                            fg='#e8e8e8', bg='#141414',
                            justify=tk.LEFT)
        info_label.pack(side=tk.LEFT, padx=20)
    
    def draw_gradient_circle(self, canvas, color, intensity):
        """Draw gradient circle effect"""
        canvas.delete("all")
        
        # Parse color
        if color.startswith('#'):
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)
        else:
            r, g, b = 128, 128, 128
        
        # Apply intensity
        intensity_factor = intensity / 255.0
        r = int(r * intensity_factor)
        g = int(g * intensity_factor)
        b = int(b * intensity_factor)
        
        # Create gradient effect
        center_x, center_y = 50, 50
        max_radius = 45
        
        for i in range(max_radius, 0, -2):
            alpha = (max_radius - i) / max_radius
            grad_r = int(r + (255 - r) * alpha * 0.3)
            grad_g = int(g + (255 - g) * alpha * 0.3)
            grad_b = int(b + (255 - b) * alpha * 0.3)
            
            grad_color = f"#{grad_r:02x}{grad_g:02x}{grad_b:02x}"
            
            x1, y1 = center_x - i, center_y - i
            x2, y2 = center_x + i, center_y + i
            canvas.create_oval(x1, y1, x2, y2, fill=grad_color, outline="")
        
        # Add intensity text
        if intensity_factor > 0.1:
            text_color = "white" if intensity_factor > 0.5 else "black"
            canvas.create_text(center_x, center_y, text=f"{intensity}", 
                             fill=text_color, font=('Segoe UI', 12, 'bold'))
    
    def update_connection_indicator(self):
        """Update connection indicator"""
        self.connection_canvas.delete("all")
        
        if self.is_connected:
            # Connected - pulsing green
            self.connection_canvas.create_oval(5, 5, 55, 55,
                                             fill='#10b981', outline='#06b6d4', width=3)
            self.connection_canvas.create_oval(15, 15, 45, 45,
                                             fill='#34d399', outline="")
            self.connection_canvas.create_oval(25, 25, 35, 35,
                                             fill='#ffffff', outline="")
            self.status_label.config(text="Connected", fg='#10b981')
            self.connect_btn.config(text="🔌 Disconnect")
        else:
            # Disconnected - red
            self.connection_canvas.create_oval(20, 20, 40, 40,
                                             fill='#ef4444', outline='#dc2626', width=2)
            self.status_label.config(text="Disconnected", fg='#ef4444')
            self.connect_btn.config(text="🔗 Connect")
    
    def toggle_connection(self):
        """Toggle connection state"""
        self.is_connected = not self.is_connected
        self.update_connection_indicator()
        
        if self.is_connected:
            messagebox.showinfo("Success", "Connected successfully!\n(This is a demo - no real Arduino needed)")
        else:
            messagebox.showinfo("Info", "Disconnected from demo Arduino")
    
    def start_demo_animations(self):
        """Start demo animations"""
        self.animate_pwm_values()
        self.animate_pulse()
    
    def animate_pwm_values(self):
        """Animate PWM values for demo"""
        def update_pwm():
            for i in range(3):
                # Simulate changing PWM values
                self.pwm_values[i] = int(128 + 100 * math.sin(time.time() * (i + 1) * 0.5))
                self.pwm_values[i] = max(0, min(255, self.pwm_values[i]))
                
                # Update display
                if i < len(self.color_canvases):
                    colors = ["#ef4444", "#3b82f6", "#10b981"]
                    self.draw_gradient_circle(self.color_canvases[i], colors[i], self.pwm_values[i])
            
            # Schedule next update
            self.root.after(100, update_pwm)
        
        # Import math for sine wave
        import math
        update_pwm()
    
    def animate_pulse(self):
        """Animate pulse indicator"""
        def pulse_animation(step=0):
            self.pulse_canvas.delete("all")
            
            # Create pulsing effect
            size = 30 + 10 * math.sin(step * 0.3)
            center = 40
            
            # Outer glow
            self.pulse_canvas.create_oval(center - size - 5, center - size - 5,
                                        center + size + 5, center + size + 5,
                                        fill='#8b5cf6', outline='#a855f7', width=2)
            
            # Inner circle
            self.pulse_canvas.create_oval(center - size, center - size,
                                        center + size, center + size,
                                        fill='#c084fc', outline="")
            
            # Center dot
            self.pulse_canvas.create_oval(center - 5, center - 5,
                                        center + 5, center + 5,
                                        fill='#ffffff', outline="")
            
            # Schedule next frame
            self.root.after(50, lambda: pulse_animation(step + 1))
        
        import math
        pulse_animation()

def main():
    """Main function"""
    root = tk.Tk()
    app = MockArduinoController(root)
    
    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() - root.winfo_width()) // 2
    y = (root.winfo_screenheight() - root.winfo_height()) // 2
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
