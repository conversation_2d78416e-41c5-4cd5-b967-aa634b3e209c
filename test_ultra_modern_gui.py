#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultra-Modern Arduino GUI Test - Enhanced with Language & Theme Switching
اختبار واجهة Arduino العصرية جداً - مع تبديل اللغة والثيم
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import random
import math

class UltraModernArduinoDemo:
    """Ultra-modern Arduino GUI demo with language and theme switching"""
    
    def __init__(self, root):
        self.root = root
        
        # Theme and language settings
        self.current_theme = "dark"
        self.current_language = "en"
        self.setup_themes()
        self.setup_translations()
        
        # Demo data
        self.pwm_values = [128, 64, 192]
        self.is_connected = False
        
        self.setup_window()
        self.create_interface()
        self.start_animations()
    
    def setup_themes(self):
        """Setup ultra-modern themes"""
        self.themes = {
            "dark": {
                "bg": "#0f0f23",
                "fg": "#f8fafc",
                "card_bg": "#312e81",
                "accent": "#06b6d4",
                "accent_hover": "#0891b2",
                "button_bg": "#7c3aed",
                "button_fg": "#ffffff",
                "button_hover": "#8b5cf6",
                "success": "#10b981",
                "error": "#ef4444",
                "warning": "#f59e0b",
                "border": "#4c1d95",
                "surface": "#262626"
            },
            "light": {
                "bg": "#f8fafc",
                "fg": "#0f172a",
                "card_bg": "#f1f5f9",
                "accent": "#0ea5e9",
                "accent_hover": "#0284c7",
                "button_bg": "#7c3aed",
                "button_fg": "#ffffff",
                "button_hover": "#8b5cf6",
                "success": "#059669",
                "error": "#ef4444",
                "warning": "#f59e0b",
                "border": "#e2e8f0",
                "surface": "#f1f5f9"
            }
        }
    
    def setup_translations(self):
        """Setup translations"""
        self.translations = {
            "en": {
                "title": "Ultra-Modern Arduino Controller",
                "subtitle": "Advanced Control Interface",
                "connection": "Connection Status",
                "connected": "Connected",
                "disconnected": "Disconnected",
                "connect": "Connect",
                "disconnect": "Disconnect",
                "pwm_control": "PWM Control",
                "red_channel": "Red Channel",
                "blue_channel": "Blue Channel", 
                "green_channel": "Green Channel",
                "animations": "Live Animations",
                "theme": "Theme",
                "language": "Language",
                "dark_mode": "Dark",
                "light_mode": "Light"
            },
            "ar": {
                "title": "تحكم Arduino عصري جداً",
                "subtitle": "واجهة تحكم متقدمة",
                "connection": "حالة الاتصال",
                "connected": "متصل",
                "disconnected": "غير متصل",
                "connect": "اتصال",
                "disconnect": "قطع الاتصال",
                "pwm_control": "تحكم PWM",
                "red_channel": "القناة الحمراء",
                "blue_channel": "القناة الزرقاء",
                "green_channel": "القناة الخضراء",
                "animations": "رسوم متحركة مباشرة",
                "theme": "المظهر",
                "language": "اللغة",
                "dark_mode": "مظلم",
                "light_mode": "فاتح"
            }
        }
    
    def t(self, key):
        """Get translated text"""
        return self.translations[self.current_language].get(key, key)
    
    def setup_window(self):
        """Setup ultra-modern window"""
        self.root.title(f"🚀 {self.t('title')}")
        self.root.geometry("1000x700")
        self.root.configure(bg=self.themes[self.current_theme]["bg"])
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() - self.root.winfo_width()) // 2
        y = (self.root.winfo_screenheight() - self.root.winfo_height()) // 2
        self.root.geometry(f"+{x}+{y}")
    
    def create_interface(self):
        """Create ultra-modern interface"""
        main_frame = tk.Frame(self.root, bg=self.themes[self.current_theme]["bg"], padx=40, pady=30)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header with controls
        self.create_header(main_frame)
        
        # Connection demo
        self.create_connection_demo(main_frame)
        
        # PWM demo
        self.create_pwm_demo(main_frame)
        
        # Animation demo
        self.create_animation_demo(main_frame)
    
    def create_header(self, parent):
        """Create ultra-modern header"""
        header_frame = tk.Frame(parent, bg=self.themes[self.current_theme]["bg"])
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # Title section
        title_frame = tk.Frame(header_frame, bg=self.themes[self.current_theme]["bg"])
        title_frame.pack(side=tk.LEFT)
        
        self.title_label = tk.Label(title_frame,
                                   text=f"🚀 {self.t('title')}",
                                   font=('Segoe UI', 22, 'bold'),
                                   fg=self.themes[self.current_theme]["accent"],
                                   bg=self.themes[self.current_theme]["bg"])
        self.title_label.pack(anchor=tk.W)
        
        self.subtitle_label = tk.Label(title_frame,
                                      text=self.t('subtitle'),
                                      font=('Segoe UI', 12, 'italic'),
                                      fg=self.themes[self.current_theme]["fg"],
                                      bg=self.themes[self.current_theme]["bg"])
        self.subtitle_label.pack(anchor=tk.W)
        
        # Controls section
        controls_frame = tk.Frame(header_frame, bg=self.themes[self.current_theme]["bg"])
        controls_frame.pack(side=tk.RIGHT)
        
        # Language button
        self.language_btn = tk.Button(controls_frame,
                                     text=f"🌐 {self.t('language')}",
                                     command=self.toggle_language,
                                     font=('Segoe UI', 10, 'bold'),
                                     bg=self.themes[self.current_theme]["button_bg"],
                                     fg=self.themes[self.current_theme]["button_fg"],
                                     activebackground=self.themes[self.current_theme]["button_hover"],
                                     relief='flat', borderwidth=0,
                                     padx=20, pady=10, cursor='hand2')
        self.language_btn.pack(side=tk.RIGHT, padx=(0, 15))
        
        # Theme button
        theme_text = f"🌙 {self.t('dark_mode')}" if self.current_theme == "light" else f"☀️ {self.t('light_mode')}"
        self.theme_btn = tk.Button(controls_frame,
                                  text=theme_text,
                                  command=self.toggle_theme,
                                  font=('Segoe UI', 10, 'bold'),
                                  bg=self.themes[self.current_theme]["accent"],
                                  fg=self.themes[self.current_theme]["button_fg"],
                                  activebackground=self.themes[self.current_theme]["accent_hover"],
                                  relief='flat', borderwidth=0,
                                  padx=20, pady=10, cursor='hand2')
        self.theme_btn.pack(side=tk.RIGHT)
    
    def create_connection_demo(self, parent):
        """Create connection status demo"""
        conn_frame = tk.LabelFrame(parent,
                                  text=f"🔌 {self.t('connection')}",
                                  font=('Segoe UI', 14, 'bold'),
                                  fg=self.themes[self.current_theme]["accent"],
                                  bg=self.themes[self.current_theme]["bg"],
                                  padx=25, pady=20)
        conn_frame.pack(fill=tk.X, pady=(0, 25))
        
        # Connection indicator
        indicator_frame = tk.Frame(conn_frame, bg=self.themes[self.current_theme]["bg"])
        indicator_frame.pack(side=tk.LEFT, padx=(0, 20))
        
        self.connection_canvas = tk.Canvas(indicator_frame, width=50, height=50,
                                         highlightthickness=0, bg=self.themes[self.current_theme]["bg"])
        self.connection_canvas.pack()
        
        # Status label
        status_text = self.t('connected') if self.is_connected else self.t('disconnected')
        self.status_label = tk.Label(indicator_frame,
                                   text=f"{'🟢' if self.is_connected else '🔴'} {status_text}",
                                   font=('Segoe UI', 12, 'bold'),
                                   fg=self.themes[self.current_theme]["success"] if self.is_connected else self.themes[self.current_theme]["error"],
                                   bg=self.themes[self.current_theme]["bg"])
        self.status_label.pack(pady=(10, 0))
        
        # Connect button
        connect_text = self.t('disconnect') if self.is_connected else self.t('connect')
        self.connect_btn = tk.Button(conn_frame,
                                    text=f"{'🔌' if self.is_connected else '🔗'} {connect_text}",
                                    command=self.toggle_connection,
                                    font=('Segoe UI', 11, 'bold'),
                                    bg=self.themes[self.current_theme]["accent"],
                                    fg=self.themes[self.current_theme]["button_fg"],
                                    activebackground=self.themes[self.current_theme]["accent_hover"],
                                    relief='flat', borderwidth=0,
                                    padx=25, pady=12, cursor='hand2')
        self.connect_btn.pack(side=tk.RIGHT)
        
        self.update_connection_indicator()
    
    def create_pwm_demo(self, parent):
        """Create PWM visualization demo"""
        pwm_frame = tk.LabelFrame(parent,
                                 text=f"🎨 {self.t('pwm_control')}",
                                 font=('Segoe UI', 14, 'bold'),
                                 fg=self.themes[self.current_theme]["accent"],
                                 bg=self.themes[self.current_theme]["bg"],
                                 padx=25, pady=20)
        pwm_frame.pack(fill=tk.X, pady=(0, 25))
        
        # PWM channels
        self.pwm_canvases = []
        channels = [
            (self.t('red_channel'), "#ef4444"),
            (self.t('blue_channel'), "#3b82f6"),
            (self.t('green_channel'), "#10b981")
        ]
        
        channels_frame = tk.Frame(pwm_frame, bg=self.themes[self.current_theme]["bg"])
        channels_frame.pack()
        
        for i, (name, color) in enumerate(channels):
            channel_frame = tk.Frame(channels_frame, bg=self.themes[self.current_theme]["bg"])
            channel_frame.pack(side=tk.LEFT, padx=30, pady=15)
            
            # Color circle
            canvas = tk.Canvas(channel_frame, width=120, height=120,
                             highlightthickness=0, bg=self.themes[self.current_theme]["bg"])
            canvas.pack()
            self.pwm_canvases.append(canvas)
            
            # Label
            label = tk.Label(channel_frame, text=name,
                           font=('Segoe UI', 11, 'bold'),
                           fg=self.themes[self.current_theme]["fg"],
                           bg=self.themes[self.current_theme]["bg"])
            label.pack(pady=(15, 5))
            
            # Value label
            value_label = tk.Label(channel_frame, text=f"{self.pwm_values[i]} ({(self.pwm_values[i]/255*100):.0f}%)",
                                 font=('Segoe UI', 10),
                                 fg=self.themes[self.current_theme]["accent"],
                                 bg=self.themes[self.current_theme]["bg"])
            value_label.pack()
            
            # Draw initial circle
            self.draw_gradient_circle(canvas, color, self.pwm_values[i])
    
    def create_animation_demo(self, parent):
        """Create animation demo"""
        anim_frame = tk.LabelFrame(parent,
                                  text=f"✨ {self.t('animations')}",
                                  font=('Segoe UI', 14, 'bold'),
                                  fg=self.themes[self.current_theme]["accent"],
                                  bg=self.themes[self.current_theme]["bg"],
                                  padx=25, pady=20)
        anim_frame.pack(fill=tk.X)
        
        # Pulse animation
        self.pulse_canvas = tk.Canvas(anim_frame, width=100, height=100,
                                    highlightthickness=0, bg=self.themes[self.current_theme]["bg"])
        self.pulse_canvas.pack(side=tk.LEFT, padx=30)
        
        # Info text
        info_text = """• Complete theme switching (Dark ↔ Light)
• Language switching (English ↔ Arabic)
• Smooth animations and transitions
• Modern gradient effects
• Non-blocking threading architecture"""
        
        info_label = tk.Label(anim_frame, text=info_text,
                            font=('Segoe UI', 11),
                            fg=self.themes[self.current_theme]["fg"],
                            bg=self.themes[self.current_theme]["bg"],
                            justify=tk.LEFT)
        info_label.pack(side=tk.LEFT, padx=30)
    
    def draw_gradient_circle(self, canvas, color, intensity):
        """Draw enhanced gradient circle"""
        canvas.delete("all")
        
        # Parse color
        r = int(color[1:3], 16)
        g = int(color[3:5], 16)
        b = int(color[5:7], 16)
        
        # Apply intensity
        intensity_factor = intensity / 255.0
        r = int(r * intensity_factor)
        g = int(g * intensity_factor)
        b = int(b * intensity_factor)
        
        # Create enhanced gradient effect
        center_x, center_y = 60, 60
        max_radius = 50
        
        # Outer glow for high intensity
        if intensity_factor > 0.7:
            glow_color = f"#{min(255, r + 30):02x}{min(255, g + 30):02x}{min(255, b + 30):02x}"
            canvas.create_oval(center_x - max_radius - 10, center_y - max_radius - 10,
                             center_x + max_radius + 10, center_y + max_radius + 10,
                             outline=glow_color, width=3)
        
        # Main gradient circles
        for i in range(max_radius, 0, -3):
            alpha = (max_radius - i) / max_radius
            grad_r = int(r + (255 - r) * alpha * 0.4)
            grad_g = int(g + (255 - g) * alpha * 0.4)
            grad_b = int(b + (255 - b) * alpha * 0.4)
            
            grad_color = f"#{grad_r:02x}{grad_g:02x}{grad_b:02x}"
            
            x1, y1 = center_x - i, center_y - i
            x2, y2 = center_x + i, center_y + i
            canvas.create_oval(x1, y1, x2, y2, fill=grad_color, outline="")
        
        # Center text
        if intensity_factor > 0.1:
            text_color = "white" if intensity_factor > 0.5 else "black"
            canvas.create_text(center_x, center_y, text=f"{intensity}",
                             fill=text_color, font=('Segoe UI', 14, 'bold'))
    
    def update_connection_indicator(self):
        """Update connection indicator"""
        self.connection_canvas.delete("all")
        
        if self.is_connected:
            # Connected - pulsing effect
            self.connection_canvas.create_oval(5, 5, 45, 45,
                                             fill=self.themes[self.current_theme]["success"],
                                             outline=self.themes[self.current_theme]["accent"], width=3)
            self.connection_canvas.create_oval(15, 15, 35, 35,
                                             fill=self.themes[self.current_theme]["accent"], outline="")
            self.connection_canvas.create_oval(20, 20, 30, 30,
                                             fill="#ffffff", outline="")
        else:
            # Disconnected
            self.connection_canvas.create_oval(15, 15, 35, 35,
                                             fill=self.themes[self.current_theme]["error"],
                                             outline=self.themes[self.current_theme]["border"], width=2)
    
    def toggle_theme(self):
        """Toggle theme and refresh UI"""
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
        self.refresh_ui()
    
    def toggle_language(self):
        """Toggle language and refresh UI"""
        self.current_language = "ar" if self.current_language == "en" else "en"
        self.refresh_ui()
    
    def toggle_connection(self):
        """Toggle connection state"""
        self.is_connected = not self.is_connected
        self.refresh_ui()
        
        status = self.t('connected') if self.is_connected else self.t('disconnected')
        messagebox.showinfo("Status", f"{status}!\n(Demo mode)")
    
    def refresh_ui(self):
        """Refresh entire UI with new theme/language"""
        # Clear and recreate interface
        for widget in self.root.winfo_children():
            widget.destroy()
        
        self.root.configure(bg=self.themes[self.current_theme]["bg"])
        self.create_interface()
    
    def start_animations(self):
        """Start demo animations"""
        self.animate_pwm()
        self.animate_pulse()
    
    def animate_pwm(self):
        """Animate PWM values"""
        def update():
            for i in range(3):
                # Simulate changing values
                self.pwm_values[i] = int(128 + 100 * math.sin(time.time() * (i + 1) * 0.8))
                self.pwm_values[i] = max(0, min(255, self.pwm_values[i]))
                
                if i < len(self.pwm_canvases):
                    colors = ["#ef4444", "#3b82f6", "#10b981"]
                    self.draw_gradient_circle(self.pwm_canvases[i], colors[i], self.pwm_values[i])
            
            self.root.after(100, update)
        
        update()
    
    def animate_pulse(self):
        """Animate pulse indicator"""
        def pulse(step=0):
            if hasattr(self, 'pulse_canvas'):
                self.pulse_canvas.delete("all")
                
                size = 35 + 15 * math.sin(step * 0.2)
                center = 50
                
                # Outer ring
                self.pulse_canvas.create_oval(center - size - 5, center - size - 5,
                                            center + size + 5, center + size + 5,
                                            fill=self.themes[self.current_theme]["accent"],
                                            outline=self.themes[self.current_theme]["success"], width=3)
                
                # Inner circle
                self.pulse_canvas.create_oval(center - size, center - size,
                                            center + size, center + size,
                                            fill=self.themes[self.current_theme]["success"], outline="")
                
                # Center dot
                self.pulse_canvas.create_oval(center - 8, center - 8,
                                            center + 8, center + 8,
                                            fill="#ffffff", outline="")
                
                self.root.after(50, lambda: pulse(step + 1))
        
        pulse()

def main():
    """Main function"""
    root = tk.Tk()
    app = UltraModernArduinoDemo(root)
    root.mainloop()

if __name__ == "__main__":
    main()
