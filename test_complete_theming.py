#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Complete Theme and Language Switching
اختبار التبديل الكامل للثيم واللغة
"""

import tkinter as tk
from tkinter import ttk, messagebox
import time

class CompleteThemingTest:
    """Test complete theme and language switching"""
    
    def __init__(self, root):
        self.root = root
        
        # Settings
        self.current_theme = "dark"
        self.current_language = "en"
        
        self.setup_themes()
        self.setup_translations()
        self.setup_window()
        self.create_interface()
    
    def setup_themes(self):
        """Setup complete themes"""
        self.themes = {
            "dark": {
                "bg": "#0f0f23",
                "fg": "#f8fafc",
                "card_bg": "#312e81",
                "accent": "#06b6d4",
                "accent_hover": "#0891b2",
                "button_bg": "#7c3aed",
                "button_fg": "#ffffff",
                "button_hover": "#8b5cf6",
                "button_active": "#6d28d9",
                "success": "#10b981",
                "error": "#ef4444",
                "warning": "#f59e0b",
                "info": "#3b82f6",
                "border": "#4c1d95",
                "border_light": "#6d28d9",
                "surface": "#262626",
                "surface_variant": "#374151",
                "entry_bg": "#1e1b4b",
                "entry_fg": "#f1f5f9",
                "select_bg": "#7c3aed",
                "select_fg": "#ffffff"
            },
            "light": {
                "bg": "#f8fafc",
                "fg": "#0f172a",
                "card_bg": "#f1f5f9",
                "accent": "#0ea5e9",
                "accent_hover": "#0284c7",
                "button_bg": "#7c3aed",
                "button_fg": "#ffffff",
                "button_hover": "#8b5cf6",
                "button_active": "#6d28d9",
                "success": "#059669",
                "error": "#ef4444",
                "warning": "#f59e0b",
                "info": "#3b82f6",
                "border": "#e2e8f0",
                "border_light": "#cbd5e1",
                "surface": "#f1f5f9",
                "surface_variant": "#e2e8f0",
                "entry_bg": "#ffffff",
                "entry_fg": "#1e293b",
                "select_bg": "#7c3aed",
                "select_fg": "#ffffff"
            }
        }
    
    def setup_translations(self):
        """Setup translations"""
        self.translations = {
            "en": {
                "title": "Complete Theme & Language Test",
                "subtitle": "Testing Full UI Refresh",
                "theme": "Theme",
                "language": "Language",
                "dark_mode": "Dark",
                "light_mode": "Light",
                "connection": "Connection Status",
                "connected": "Connected",
                "disconnected": "Disconnected",
                "connect": "Connect",
                "disconnect": "Disconnect",
                "test_button": "Test Button",
                "sample_text": "This is sample text to test theming",
                "status_info": "Status: All systems operational",
                "log_entry": "Log entry with timestamp"
            },
            "ar": {
                "title": "اختبار الثيم واللغة الكامل",
                "subtitle": "اختبار تحديث الواجهة الكامل",
                "theme": "المظهر",
                "language": "اللغة",
                "dark_mode": "مظلم",
                "light_mode": "فاتح",
                "connection": "حالة الاتصال",
                "connected": "متصل",
                "disconnected": "غير متصل",
                "connect": "اتصال",
                "disconnect": "قطع الاتصال",
                "test_button": "زر اختبار",
                "sample_text": "هذا نص تجريبي لاختبار الثيم",
                "status_info": "الحالة: جميع الأنظمة تعمل",
                "log_entry": "إدخال سجل مع الوقت"
            }
        }
    
    def t(self, key):
        """Get translated text"""
        return self.translations[self.current_language].get(key, key)
    
    def setup_window(self):
        """Setup window"""
        self.root.title(self.t('title'))
        self.root.geometry("900x600")
        self.root.configure(bg=self.themes[self.current_theme]["bg"])
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() - self.root.winfo_width()) // 2
        y = (self.root.winfo_screenheight() - self.root.winfo_height()) // 2
        self.root.geometry(f"+{x}+{y}")
    
    def create_interface(self):
        """Create complete interface"""
        # Main container
        self.main_frame = tk.Frame(self.root, bg=self.themes[self.current_theme]["bg"], padx=30, pady=20)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header
        self.create_header()
        
        # Content sections
        self.create_connection_section()
        self.create_controls_section()
        self.create_log_section()
    
    def create_header(self):
        """Create header with controls"""
        header_frame = tk.Frame(self.main_frame, bg=self.themes[self.current_theme]["bg"])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Title section
        title_frame = tk.Frame(header_frame, bg=self.themes[self.current_theme]["bg"])
        title_frame.pack(side=tk.LEFT)
        
        self.title_label = tk.Label(title_frame,
                                   text=f"🚀 {self.t('title')}",
                                   font=('Segoe UI', 18, 'bold'),
                                   fg=self.themes[self.current_theme]["accent"],
                                   bg=self.themes[self.current_theme]["bg"])
        self.title_label.pack(anchor=tk.W)
        
        self.subtitle_label = tk.Label(title_frame,
                                      text=self.t('subtitle'),
                                      font=('Segoe UI', 11, 'italic'),
                                      fg=self.themes[self.current_theme]["fg"],
                                      bg=self.themes[self.current_theme]["bg"])
        self.subtitle_label.pack(anchor=tk.W)
        
        # Controls
        controls_frame = tk.Frame(header_frame, bg=self.themes[self.current_theme]["bg"])
        controls_frame.pack(side=tk.RIGHT)
        
        # Language button
        self.language_btn = tk.Button(controls_frame,
                                     text=f"🌐 {self.t('language')}",
                                     command=self.toggle_language,
                                     font=('Segoe UI', 10, 'bold'),
                                     bg=self.themes[self.current_theme]["button_bg"],
                                     fg=self.themes[self.current_theme]["button_fg"],
                                     activebackground=self.themes[self.current_theme]["button_hover"],
                                     relief='flat', borderwidth=0,
                                     padx=20, pady=10, cursor='hand2')
        self.language_btn.pack(side=tk.RIGHT, padx=(0, 15))
        
        # Theme button
        theme_text = f"🌙 {self.t('dark_mode')}" if self.current_theme == "light" else f"☀️ {self.t('light_mode')}"
        self.theme_btn = tk.Button(controls_frame,
                                  text=theme_text,
                                  command=self.toggle_theme,
                                  font=('Segoe UI', 10, 'bold'),
                                  bg=self.themes[self.current_theme]["accent"],
                                  fg=self.themes[self.current_theme]["button_fg"],
                                  activebackground=self.themes[self.current_theme]["accent_hover"],
                                  relief='flat', borderwidth=0,
                                  padx=20, pady=10, cursor='hand2')
        self.theme_btn.pack(side=tk.RIGHT)
    
    def create_connection_section(self):
        """Create connection section"""
        conn_frame = tk.LabelFrame(self.main_frame,
                                  text=f"🔌 {self.t('connection')}",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg=self.themes[self.current_theme]["accent"],
                                  bg=self.themes[self.current_theme]["bg"],
                                  padx=20, pady=15)
        conn_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Status
        self.status_label = tk.Label(conn_frame,
                                    text=f"🔴 {self.t('disconnected')}",
                                    font=('Segoe UI', 11, 'bold'),
                                    fg=self.themes[self.current_theme]["error"],
                                    bg=self.themes[self.current_theme]["bg"])
        self.status_label.pack(side=tk.LEFT)
        
        # Connect button
        self.connect_btn = tk.Button(conn_frame,
                                    text=f"🔗 {self.t('connect')}",
                                    command=self.toggle_connection,
                                    font=('Segoe UI', 10, 'bold'),
                                    bg=self.themes[self.current_theme]["accent"],
                                    fg=self.themes[self.current_theme]["button_fg"],
                                    activebackground=self.themes[self.current_theme]["accent_hover"],
                                    relief='flat', borderwidth=0,
                                    padx=20, pady=8, cursor='hand2')
        self.connect_btn.pack(side=tk.RIGHT)
        
        self.is_connected = False
    
    def create_controls_section(self):
        """Create controls section"""
        controls_frame = tk.LabelFrame(self.main_frame,
                                      text="🎛️ Controls",
                                      font=('Segoe UI', 12, 'bold'),
                                      fg=self.themes[self.current_theme]["accent"],
                                      bg=self.themes[self.current_theme]["bg"],
                                      padx=20, pady=15)
        controls_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Sample text
        self.sample_label = tk.Label(controls_frame,
                                    text=self.t('sample_text'),
                                    font=('Segoe UI', 11),
                                    fg=self.themes[self.current_theme]["fg"],
                                    bg=self.themes[self.current_theme]["bg"])
        self.sample_label.pack(side=tk.LEFT)
        
        # Test button
        self.test_btn = tk.Button(controls_frame,
                                 text=self.t('test_button'),
                                 command=self.test_action,
                                 font=('Segoe UI', 10, 'bold'),
                                 bg=self.themes[self.current_theme]["success"],
                                 fg=self.themes[self.current_theme]["button_fg"],
                                 activebackground=self.themes[self.current_theme]["button_hover"],
                                 relief='flat', borderwidth=0,
                                 padx=20, pady=8, cursor='hand2')
        self.test_btn.pack(side=tk.RIGHT)
    
    def create_log_section(self):
        """Create log section"""
        log_frame = tk.LabelFrame(self.main_frame,
                                 text="📝 Log",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg=self.themes[self.current_theme]["accent"],
                                 bg=self.themes[self.current_theme]["bg"],
                                 padx=20, pady=15)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # Log text
        self.log_text = tk.Text(log_frame,
                               height=8,
                               bg=self.themes[self.current_theme]["entry_bg"],
                               fg=self.themes[self.current_theme]["entry_fg"],
                               insertbackground=self.themes[self.current_theme]["fg"],
                               selectbackground=self.themes[self.current_theme]["select_bg"],
                               selectforeground=self.themes[self.current_theme]["select_fg"],
                               font=('Consolas', 10),
                               relief='flat', borderwidth=2)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Add initial log entry
        self.add_log_entry("System initialized", "info")
    
    def toggle_theme(self):
        """Toggle theme with complete refresh"""
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
        self.refresh_complete_ui()
        self.add_log_entry(f"Theme changed to {self.current_theme}", "info")
    
    def toggle_language(self):
        """Toggle language with complete refresh"""
        self.current_language = "ar" if self.current_language == "en" else "en"
        self.refresh_complete_ui()
        self.add_log_entry(f"Language changed to {self.current_language}", "info")
    
    def toggle_connection(self):
        """Toggle connection state"""
        self.is_connected = not self.is_connected
        self.refresh_complete_ui()
        status = "connected" if self.is_connected else "disconnected"
        self.add_log_entry(f"Connection {status}", "success" if self.is_connected else "warning")
    
    def test_action(self):
        """Test action"""
        self.add_log_entry(self.t('log_entry'), "success")
        messagebox.showinfo("Test", f"Theme: {self.current_theme}\nLanguage: {self.current_language}")
    
    def refresh_complete_ui(self):
        """Refresh complete UI with new theme/language"""
        theme = self.themes[self.current_theme]
        
        # Update window
        self.root.title(self.t('title'))
        self.root.configure(bg=theme["bg"])
        self.main_frame.configure(bg=theme["bg"])
        
        # Update header
        self.title_label.config(text=f"🚀 {self.t('title')}", fg=theme["accent"], bg=theme["bg"])
        self.subtitle_label.config(text=self.t('subtitle'), fg=theme["fg"], bg=theme["bg"])
        
        # Update buttons
        self.language_btn.config(text=f"🌐 {self.t('language')}", bg=theme["button_bg"], 
                                fg=theme["button_fg"], activebackground=theme["button_hover"])
        
        theme_text = f"🌙 {self.t('dark_mode')}" if self.current_theme == "light" else f"☀️ {self.t('light_mode')}"
        self.theme_btn.config(text=theme_text, bg=theme["accent"], 
                             fg=theme["button_fg"], activebackground=theme["accent_hover"])
        
        # Update connection section
        status_text = self.t('connected') if self.is_connected else self.t('disconnected')
        status_color = theme["success"] if self.is_connected else theme["error"]
        self.status_label.config(text=f"{'🟢' if self.is_connected else '🔴'} {status_text}",
                                fg=status_color, bg=theme["bg"])
        
        connect_text = self.t('disconnect') if self.is_connected else self.t('connect')
        self.connect_btn.config(text=f"{'🔌' if self.is_connected else '🔗'} {connect_text}",
                               bg=theme["accent"], fg=theme["button_fg"], 
                               activebackground=theme["accent_hover"])
        
        # Update controls section
        self.sample_label.config(text=self.t('sample_text'), fg=theme["fg"], bg=theme["bg"])
        self.test_btn.config(text=self.t('test_button'), bg=theme["success"], 
                            fg=theme["button_fg"], activebackground=theme["button_hover"])
        
        # Update log
        self.log_text.config(bg=theme["entry_bg"], fg=theme["entry_fg"],
                            insertbackground=theme["fg"], selectbackground=theme["select_bg"],
                            selectforeground=theme["select_fg"])
        
        # Force redraw
        self.root.update_idletasks()
    
    def add_log_entry(self, message, level="info"):
        """Add log entry"""
        timestamp = time.strftime("%H:%M:%S")
        colors = {
            "info": self.themes[self.current_theme]["info"],
            "success": self.themes[self.current_theme]["success"],
            "warning": self.themes[self.current_theme]["warning"],
            "error": self.themes[self.current_theme]["error"]
        }
        
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)

def main():
    """Main function"""
    root = tk.Tk()
    app = CompleteThemingTest(root)
    root.mainloop()

if __name__ == "__main__":
    main()
