#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Arduino Serial Controller GUI - Ultra Modern Dark Mode & Fully Responsive
Professional Arduino Control Interface with Terminal-Style Serial Communication
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import serial
import serial.tools.list_ports
import threading
import time
import json
import os
import queue
from datetime import datetime
import math

class ModernArduinoController:
    def __init__(self, root):
        self.root = root

        # Theme and language settings (Dark mode only, English only)
        self.current_theme = "dark"  # Always dark mode
        self.current_language = "en"  # English only
        self.themes = self.setup_themes()
        self.translations = self.setup_translations()

        # Settings file
        self.settings_file = "arduino_gui_settings.json"
        self.load_settings()

        # Serial terminal visibility
        self.terminal_visible = True
        self.terminal_height = 200  # Default terminal height

        # Now setup window and styles
        self.setup_window()
        self.setup_styles()

        # Threading and communication
        self.command_queue = queue.Queue()
        self.response_queue = queue.Queue()
        self.serial_thread = None
        self.running = True

        # Serial connection
        self.serial_connection = None
        self.is_connected = False
        self.auto_refresh = False
        self.connection_lock = threading.Lock()

        # Data storage
        self.pwm_values = [0, 0, 0]
        self.stepper_data = {"angle": 0.0, "speed": 12, "mode": "IDLE"}
        self.relay_data = {
            "right": {"active": False, "timer": 5, "remaining": 0},
            "left": {"active": False, "timer": 5, "remaining": 0}
        }
        self.shoot_rate = 1
        self.continuous_shooting = False

        # Animation and visual effects
        self.animation_running = False
        self.pulse_animation_id = None
        self.connection_pulse_id = None

        # Apply theme first before creating widgets
        self.apply_theme()

        self.create_widgets()
        self.refresh_ports()

        # Start background threads
        self.start_serial_thread()
        self.start_response_handler()

        # Auto refresh timer
        self.auto_refresh_timer()

        # Responsive design
        self.setup_responsive_design()

        # Start visual effects
        self.start_visual_effects()

    def start_serial_thread(self):
        """Start background serial communication thread"""
        self.serial_thread = threading.Thread(target=self.serial_worker, daemon=True)
        self.serial_thread.start()

    def serial_worker(self):
        """Background thread for serial communication"""
        while self.running:
            try:
                # Check for commands to send
                if not self.command_queue.empty():
                    command, callback = self.command_queue.get(timeout=0.1)

                    with self.connection_lock:
                        if self.is_connected and self.serial_connection:
                            try:
                                # Send command
                                self.serial_connection.write((command + '\n').encode())
                                time.sleep(0.05)  # Small delay

                                # Read response
                                response = ""
                                start_time = time.time()
                                while time.time() - start_time < 1.0:  # 1 second timeout
                                    if self.serial_connection.in_waiting:
                                        response = self.serial_connection.readline().decode().strip()
                                        break
                                    time.sleep(0.01)

                                # Put response in queue
                                self.response_queue.put((command, response, callback))

                            except Exception as e:
                                self.response_queue.put((command, f"ERROR: {str(e)}", callback))
                        else:
                            self.response_queue.put((command, "ERROR: Not connected", callback))

                time.sleep(0.01)  # Small delay to prevent high CPU usage

            except queue.Empty:
                continue
            except Exception as e:
                print(f"Serial thread error: {e}")

    def start_response_handler(self):
        """Start response handler for GUI updates"""
        self.process_responses()

    def process_responses(self):
        """Process responses from serial thread"""
        try:
            while not self.response_queue.empty():
                command, response, callback = self.response_queue.get_nowait()

                # Log the response
                self.log_response(command, response)

                # Execute callback if provided
                if callback:
                    callback(response)

        except queue.Empty:
            pass
        except Exception as e:
            print(f"Response handler error: {e}")

        # Schedule next check
        self.root.after(50, self.process_responses)

    def start_visual_effects(self):
        """Start visual effects and animations"""
        self.animate_connection_indicator()

    def animate_connection_indicator(self):
        """Animate connection indicator with pulsing effect"""
        if hasattr(self, 'connection_indicator'):
            if self.is_connected:
                # Pulsing green effect
                self.connection_pulse_id = self.root.after(1000, self.pulse_connection_indicator)
            else:
                # Static red
                self.update_connection_indicator()

    def pulse_connection_indicator(self):
        """Create pulsing effect for connection indicator"""
        if hasattr(self, 'connection_indicator') and self.is_connected:
            # Create pulsing effect
            self.connection_indicator.delete("all")

            # Outer glow
            self.connection_indicator.create_oval(0, 0, 20, 20,
                                                fill=self.themes[self.current_theme]["success"],
                                                outline=self.themes[self.current_theme]["accent"],
                                                width=2)
            # Inner circle
            self.connection_indicator.create_oval(4, 4, 16, 16,
                                                fill=self.themes[self.current_theme]["accent"],
                                                outline="")

            # Schedule next pulse
            self.connection_pulse_id = self.root.after(1500, self.animate_connection_indicator)

    def t(self, key):
        """Get translated text for current language"""
        return self.translations[self.current_language].get(key, key)

    def setup_window(self):
        """Setup fully responsive window with dark mode terminal-style design"""
        self.root.title(f"⚡ {self.t('app_title')} - Professional Dark Interface")

        # Get screen dimensions for full responsiveness
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Responsive sizing for all screen types with better mobile support
        if screen_width <= 480:  # Small phones
            width, height = int(screen_width * 0.98), int(screen_height * 0.95)
            self.is_mobile = True
            self.terminal_height = 120  # Smaller terminal on mobile
        elif screen_width <= 768:  # Large phones/small tablets
            width, height = int(screen_width * 0.95), int(screen_height * 0.92)
            self.is_mobile = True
            self.terminal_height = 150
        elif screen_width <= 1024:  # Tablets/small laptops
            width, height = int(screen_width * 0.90), int(screen_height * 0.88)
            self.is_mobile = False
            self.terminal_height = 180
        elif screen_width <= 1366:  # Standard laptops
            width, height = int(screen_width * 0.85), int(screen_height * 0.85)
            self.is_mobile = False
            self.terminal_height = 200
        else:  # Large screens/desktops
            width, height = min(1600, int(screen_width * 0.80)), min(1000, int(screen_height * 0.85))
            self.is_mobile = False
            self.terminal_height = 250

        # Center window on screen
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        self.root.geometry(f"{width}x{height}+{x}+{y}")

        # Dynamic minimum size based on screen
        min_width = min(900, int(screen_width * 0.6))
        min_height = min(700, int(screen_height * 0.6))
        self.root.minsize(min_width, min_height)

        # Dark mode terminal-style window
        self.root.configure(bg=self.themes[self.current_theme]["bg"])

        # Configure fully responsive grid with terminal at bottom
        self.root.rowconfigure(0, weight=1)  # Main content area
        self.root.rowconfigure(1, weight=0)  # Terminal area (fixed height)
        self.root.columnconfigure(0, weight=1)

        # Bind resize events for dynamic responsiveness
        self.root.bind('<Configure>', self.on_window_resize)

        # Store initial dimensions for responsive calculations
        self.initial_width = width
        self.initial_height = height
        self.screen_width = screen_width
        self.screen_height = screen_height

    def on_window_resize(self, event):
        """Handle window resize for responsive design"""
        if event.widget == self.root:
            # Update background color on resize
            self.root.configure(bg=self.themes[self.current_theme]["bg"])

            # Adjust layout based on window size
            if hasattr(self, 'main_container'):
                # Force refresh of all backgrounds
                self.refresh_all_backgrounds()

            # Adjust terminal height based on window size
            if hasattr(self, 'terminal_frame') and self.terminal_visible:
                new_height = max(120, min(300, int(event.height * 0.25)))
                if new_height != self.terminal_height:
                    self.terminal_height = new_height
                    self.update_terminal_size()

    def update_terminal_size(self):
        """Update terminal size based on current settings"""
        if hasattr(self, 'terminal_frame') and self.terminal_visible:
            self.terminal_frame.configure(height=self.terminal_height)
            if hasattr(self, 'log_text'):
                # Calculate lines based on height
                lines = max(8, self.terminal_height // 20)
                self.log_text.configure(height=lines)

    def setup_styles(self):
        """Setup modern ttk styles"""
        self.style = ttk.Style()
        # Configure modern styles
        self.style.theme_use('clam')

    def setup_themes(self):
        """Setup terminal-style dark theme only"""
        return {
            "dark": {
                # Terminal-style colors
                "bg": "#0d1117",              # GitHub dark background
                "fg": "#f0f6fc",              # Bright white text
                "select_bg": "#1f6feb",       # Blue selection
                "select_fg": "#ffffff",       # White selected text
                "entry_bg": "#161b22",        # Dark input background
                "entry_fg": "#f0f6fc",        # Light input text
                "button_bg": "#238636",       # Green buttons
                "button_fg": "#ffffff",       # White button text
                "button_hover": "#2ea043",    # Lighter green hover
                "button_active": "#1a7f37",   # Darker green active
                "frame_bg": "#161b22",        # Dark frame
                "card_bg": "#21262d",         # Card background
                "accent": "#58a6ff",          # Blue accent
                "accent_hover": "#79c0ff",    # Light blue hover
                "warning": "#f85149",         # Red warning
                "error": "#f85149",           # Red error
                "success": "#56d364",         # Green success
                "info": "#79c0ff",            # Blue info
                "border": "#30363d",          # Dark border
                "border_light": "#484f58",    # Lighter border
                "shadow": "#000000",          # Black shadow
                "surface": "#21262d",         # Surface color
                "surface_variant": "#30363d", # Surface variant

                # Terminal specific colors
                "terminal_bg": "#0d1117",     # Terminal background
                "terminal_fg": "#f0f6fc",     # Terminal text
                "terminal_cursor": "#58a6ff", # Terminal cursor
                "terminal_selection": "#1f6feb", # Terminal selection

                # Command colors (like terminal)
                "cmd_input": "#56d364",       # Green for input
                "cmd_output": "#f0f6fc",      # White for output
                "cmd_error": "#f85149",       # Red for errors
                "cmd_success": "#56d364",     # Green for success
                "cmd_info": "#79c0ff",        # Blue for info
                "cmd_warning": "#e3b341",     # Yellow for warnings
                "cmd_timestamp": "#8b949e"    # Gray for timestamps
            }
        }

    def setup_translations(self):
        """Setup translations - English only interface"""
        return {
            "en": {
                "app_title": "Arduino Controller Pro",
                "app_subtitle": "Professional Arduino Control Interface",
                "serial_connection": "Serial Connection",
                "port": "Port",
                "speed": "Speed",
                "refresh": "Refresh",
                "connect": "Connect",
                "disconnect": "Disconnect",
                "connecting": "Connecting...",
                "connected": "Connected",
                "disconnected": "Disconnected",
                "pwm_control": "PWM Control",
                "red_channel": "Red Channel (D9)",
                "blue_channel": "Blue Channel (D6)",
                "green_channel": "Green Channel (D5)",
                "pulse_control": "Pulse Control",
                "single_pulse": "Single Pulse",
                "send_pulse": "Send Pulse",
                "continuous_pulses": "Continuous Pulses",
                "pulse_rate": "Pulse Rate",
                "start_pulses": "Start Pulses",
                "stop_pulses": "Stop Pulses",
                "status": "Status",
                "stopped": "Stopped",
                "active": "Active",
                "stepper_motor": "Stepper Motor",
                "motor_status": "Motor Status",
                "angle_control": "Angle Control",
                "target_angle": "Target Angle (0-359)",
                "goto_angle": "Go to Angle",
                "speed_rpm": "Speed (1-20 RPM)",
                "movement_control": "Movement Control",
                "clockwise": "Clockwise",
                "counter_clockwise": "Counter-Clockwise",
                "stop_motor": "Stop Motor",
                "reset_position": "Reset Position",
                "relay_control": "Relay Control",
                "right_relay": "Right Relay (D10)",
                "left_relay": "Left Relay (D11)",
                "timer_seconds": "Timer (seconds, 0 for manual)",
                "turn_on": "Turn On",
                "turn_off": "Turn Off",
                "settings": "Settings",
                "auto_refresh": "Auto Refresh",
                "auto_refresh_desc": "Auto refresh status every 2 seconds",
                "manual_refresh": "Manual Refresh",
                "save_settings": "Save Settings",
                "save_to_arduino": "Save Settings to Arduino",
                "reset_arduino": "Reset Arduino to Defaults",
                "event_log": "Serial Terminal",
                "communication_log": "Serial Communication",
                "clear_log": "Clear",
                "auto_scroll": "Auto Scroll",
                "language": "Language",
                "english": "English",
                "terminal": "Terminal",
                "command": "Command",
                "response": "Response",
                "hide_terminal": "Hide Terminal",
                "show_terminal": "Show Terminal"
            }
        }

    def apply_theme(self):
        """Apply current theme to ALL widgets with complete refresh"""
        theme = self.themes[self.current_theme]

        # Configure root window
        self.root.configure(bg=theme["bg"])

        # Configure ttk styles with enhanced theming
        try:
            self.style.configure('Modern.TFrame',
                               background=theme["card_bg"],
                               relief='flat',
                               borderwidth=1)

            self.style.configure('Modern.TLabelFrame',
                               background=theme["card_bg"],
                               foreground=theme["accent"],
                               relief='flat',
                               borderwidth=2,
                               labelmargins=(15, 8, 15, 8))

            self.style.configure('Modern.TLabel',
                               background=theme["card_bg"],
                               foreground=theme["fg"],
                               font=('Segoe UI', 11))

            self.style.configure('Title.TLabel',
                               background=theme["bg"],
                               foreground=theme["accent"],
                               font=('Segoe UI', 18, 'bold'))

            self.style.configure('Modern.TButton',
                               background=theme["button_bg"],
                               foreground=theme["button_fg"],
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 10, 'bold'),
                               padding=(18, 10))

            self.style.map('Modern.TButton',
                          background=[('active', theme["button_hover"]),
                                    ('pressed', theme["button_active"])])

            self.style.configure('Accent.TButton',
                               background=theme["accent"],
                               foreground=theme["button_fg"],
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 11, 'bold'),
                               padding=(22, 12))

            self.style.map('Accent.TButton',
                          background=[('active', theme["accent_hover"]),
                                    ('pressed', theme["button_active"])])

            self.style.configure('Success.TButton',
                               background=theme["success"],
                               foreground='#ffffff',
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 10, 'bold'),
                               padding=(18, 10))

            self.style.configure('Warning.TButton',
                               background=theme["error"],
                               foreground='#ffffff',
                               borderwidth=0,
                               focuscolor='none',
                               font=('Segoe UI', 10, 'bold'),
                               padding=(18, 10))

            self.style.configure('Modern.TCombobox',
                               fieldbackground=theme["entry_bg"],
                               background=theme["entry_bg"],
                               foreground=theme["entry_fg"],
                               borderwidth=1,
                               insertcolor=theme["fg"])

            self.style.configure('Modern.TEntry',
                               fieldbackground=theme["entry_bg"],
                               foreground=theme["entry_fg"],
                               borderwidth=1,
                               insertcolor=theme["fg"])

            self.style.configure('Modern.TNotebook',
                               background=theme["bg"],
                               borderwidth=0,
                               tabmargins=(5, 8, 5, 0))

            self.style.configure('Modern.TNotebook.Tab',
                               background=theme["surface"],
                               foreground=theme["fg"],
                               padding=(25, 15),
                               borderwidth=0,
                               font=('Segoe UI', 11, 'bold'))

            self.style.map('Modern.TNotebook.Tab',
                          background=[('selected', theme["accent"]),
                                    ('active', theme["accent_hover"])],
                          foreground=[('selected', '#ffffff'),
                                    ('active', '#ffffff')])
        except Exception as e:
            print(f"Warning: Could not apply some styles: {e}")

        # Force complete UI refresh after theme change
        if hasattr(self, 'main_container'):
            self.refresh_all_ui_elements()

    def setup_responsive_design(self):
        """Setup responsive design handlers"""
        self.root.bind('<Configure>', self.on_window_resize)

    def create_widgets(self):
        """Create responsive widgets with fixed terminal at bottom"""
        # Create main content area (scrollable)
        self.main_canvas = tk.Canvas(self.root,
                                    bg=self.themes[self.current_theme]["bg"],
                                    highlightthickness=0)
        self.main_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Create vertical scrollbar for main content
        self.scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=self.main_canvas.yview)
        self.scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.main_canvas.configure(yscrollcommand=self.scrollbar.set)

        # Create scrollable frame for main content
        self.scrollable_frame = tk.Frame(self.main_canvas, bg=self.themes[self.current_theme]["bg"])
        self.canvas_window = self.main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")

        # Main container inside scrollable frame
        self.main_container = tk.Frame(self.scrollable_frame,
                                      bg=self.themes[self.current_theme]["bg"],
                                      padx=25, pady=20)
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # Configure scrolling events
        self.scrollable_frame.bind("<Configure>", self.on_frame_configure)
        self.main_canvas.bind("<Configure>", self.on_canvas_configure)

        # Bind mousewheel for scrolling
        self.main_canvas.bind("<MouseWheel>", self.on_mousewheel)
        self.root.bind("<MouseWheel>", self.on_mousewheel)

        # Configure grid weights for main content
        self.root.columnconfigure(0, weight=1)

        # Create UI components in main area
        self.create_header()
        self.create_modern_connection_frame()
        self.create_modern_control_tabs()

        # Create fixed terminal at bottom
        self.create_fixed_terminal()

    def on_frame_configure(self, event):
        """Configure scroll region when frame size changes"""
        self.main_canvas.configure(scrollregion=self.main_canvas.bbox("all"))

    def on_canvas_configure(self, event):
        """Configure canvas window width when canvas size changes"""
        canvas_width = event.width
        self.main_canvas.itemconfig(self.canvas_window, width=canvas_width)

    def on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.main_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

    def refresh_all_backgrounds(self):
        """Refresh all background colors to match current theme"""
        theme = self.themes[self.current_theme]

        # Update main canvas and scrollable frame
        if hasattr(self, 'main_canvas'):
            self.main_canvas.configure(bg=theme["bg"])

        if hasattr(self, 'scrollable_frame'):
            self.scrollable_frame.configure(bg=theme["bg"])

        if hasattr(self, 'main_container'):
            self.main_container.configure(bg=theme["bg"])

        # Force update
        self.root.update_idletasks()

    def create_header(self):
        """Create ultra-modern header with title and controls"""
        header_frame = tk.Frame(self.main_container, bg=self.themes[self.current_theme]["bg"])
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 30))
        header_frame.columnconfigure(1, weight=1)

        # App icon and title with modern styling
        title_frame = tk.Frame(header_frame, bg=self.themes[self.current_theme]["bg"])
        title_frame.grid(row=0, column=0, sticky=(tk.W))

        # Main title with gradient effect
        self.title_label = tk.Label(title_frame,
                                   text=f"🚀 {self.t('app_title')}",
                                   font=('Segoe UI', 20, 'bold'),
                                   fg=self.themes[self.current_theme]["accent"],
                                   bg=self.themes[self.current_theme]["bg"])
        self.title_label.pack(side=tk.LEFT)

        # Subtitle
        self.subtitle_label = tk.Label(title_frame,
                                      text=self.t('app_subtitle'),
                                      font=('Segoe UI', 11, 'italic'),
                                      fg=self.themes[self.current_theme]["fg"],
                                      bg=self.themes[self.current_theme]["bg"])
        self.subtitle_label.pack(side=tk.LEFT, padx=(15, 0))

        # Control buttons frame with modern styling
        controls_frame = tk.Frame(header_frame, bg=self.themes[self.current_theme]["bg"])
        controls_frame.grid(row=0, column=2, sticky=(tk.E))

        # Terminal toggle button
        self.terminal_toggle_btn = tk.Button(controls_frame,
                                           text="📟 Hide Terminal",
                                           command=self.toggle_terminal,
                                           font=('Segoe UI', 9, 'bold'),
                                           bg=self.themes[self.current_theme]["button_bg"],
                                           fg=self.themes[self.current_theme]["button_fg"],
                                           activebackground=self.themes[self.current_theme]["button_hover"],
                                           activeforeground=self.themes[self.current_theme]["button_fg"],
                                           relief='flat',
                                           borderwidth=0,
                                           padx=15, pady=8,
                                           cursor='hand2')
        self.terminal_toggle_btn.pack(side=tk.RIGHT, padx=(0, 10))

        # Fullscreen button
        self.fullscreen_btn = tk.Button(controls_frame,
                                       text="⛶",
                                       command=self.toggle_fullscreen,
                                       font=('Segoe UI', 12, 'bold'),
                                       bg=self.themes[self.current_theme]["surface"],
                                       fg=self.themes[self.current_theme]["fg"],
                                       activebackground=self.themes[self.current_theme]["surface_variant"],
                                       activeforeground=self.themes[self.current_theme]["fg"],
                                       relief='flat',
                                       borderwidth=0,
                                       padx=12, pady=8,
                                       cursor='hand2')
        self.fullscreen_btn.pack(side=tk.RIGHT)

    def toggle_terminal(self):
        """Toggle terminal visibility"""
        if self.terminal_visible:
            self.hide_terminal()
        else:
            self.show_terminal()

    def hide_terminal(self):
        """Hide the terminal"""
        if hasattr(self, 'terminal_frame'):
            self.terminal_frame.grid_remove()
            self.terminal_visible = False
            self.terminal_toggle_btn.config(text="📟 Show Terminal")
            # Adjust main content to take full space
            self.root.rowconfigure(1, weight=0)

    def show_terminal(self):
        """Show the terminal"""
        if hasattr(self, 'terminal_frame'):
            self.terminal_frame.grid()
            self.terminal_visible = True
            self.terminal_toggle_btn.config(text="📟 Hide Terminal")
            # Restore terminal space
            self.root.rowconfigure(1, weight=0)

    def toggle_fullscreen(self):
        """Toggle fullscreen mode"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)

        if current_state:
            self.fullscreen_btn.config(text="⛶")
        else:
            self.fullscreen_btn.config(text="🗗")

    def create_fixed_terminal(self):
        """Create fixed terminal at bottom of window"""
        # Terminal frame fixed at bottom
        self.terminal_frame = tk.LabelFrame(self.root,
                                          text=f"📟 {self.t('event_log')}",
                                          font=('Segoe UI', 12, 'bold'),
                                          fg=self.themes[self.current_theme]["accent"],
                                          bg=self.themes[self.current_theme]["bg"],
                                          labelanchor='nw',
                                          padx=15, pady=10,
                                          relief='flat',
                                          borderwidth=2,
                                          highlightbackground=self.themes[self.current_theme]["border"],
                                          height=self.terminal_height)
        self.terminal_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.S), padx=10, pady=(0, 10))
        self.terminal_frame.grid_propagate(False)  # Maintain fixed height
        self.terminal_frame.columnconfigure(0, weight=1)
        self.terminal_frame.rowconfigure(1, weight=1)

        # Terminal controls
        terminal_controls = tk.Frame(self.terminal_frame, bg=self.themes[self.current_theme]["bg"])
        terminal_controls.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        terminal_controls.columnconfigure(0, weight=1)

        # Terminal title and controls
        title_frame = tk.Frame(terminal_controls, bg=self.themes[self.current_theme]["bg"])
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        title_frame.columnconfigure(0, weight=1)

        # Title label
        title_label = tk.Label(title_frame,
                              text=f"📝 {self.t('communication_log')}",
                              font=('Segoe UI', 10, 'bold'),
                              fg=self.themes[self.current_theme]["fg"],
                              bg=self.themes[self.current_theme]["bg"])
        title_label.pack(side=tk.LEFT)

        # Terminal control buttons
        terminal_btn_frame = tk.Frame(title_frame, bg=self.themes[self.current_theme]["bg"])
        terminal_btn_frame.pack(side=tk.RIGHT)

        # Clear button
        clear_btn = tk.Button(terminal_btn_frame,
                             text=f"🗑️ {self.t('clear_log')}",
                             command=self.clear_log,
                             font=('Segoe UI', 8, 'bold'),
                             bg=self.themes[self.current_theme]["surface"],
                             fg=self.themes[self.current_theme]["fg"],
                             activebackground=self.themes[self.current_theme]["surface_variant"],
                             activeforeground=self.themes[self.current_theme]["fg"],
                             relief='flat',
                             borderwidth=0,
                             padx=10, pady=4,
                             cursor='hand2')
        clear_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # Auto scroll checkbox
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_check = tk.Checkbutton(terminal_btn_frame,
                                          text=self.t('auto_scroll'),
                                          variable=self.auto_scroll_var,
                                          font=('Segoe UI', 8),
                                          fg=self.themes[self.current_theme]["fg"],
                                          bg=self.themes[self.current_theme]["bg"],
                                          activeforeground=self.themes[self.current_theme]["fg"],
                                          activebackground=self.themes[self.current_theme]["bg"],
                                          selectcolor=self.themes[self.current_theme]["accent"])
        auto_scroll_check.pack(side=tk.RIGHT, padx=(5, 0))

        # Terminal text area
        terminal_container = tk.Frame(self.terminal_frame, bg=self.themes[self.current_theme]["bg"])
        terminal_container.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        terminal_container.columnconfigure(0, weight=1)
        terminal_container.rowconfigure(0, weight=1)

        # Calculate lines based on terminal height
        lines = max(8, self.terminal_height // 20)

        # Terminal-style log text area
        self.log_text = scrolledtext.ScrolledText(
            terminal_container,
            height=lines,
            width=100,
            bg=self.themes[self.current_theme]["terminal_bg"],
            fg=self.themes[self.current_theme]["terminal_fg"],
            insertbackground=self.themes[self.current_theme]["terminal_cursor"],
            selectbackground=self.themes[self.current_theme]["terminal_selection"],
            selectforeground=self.themes[self.current_theme]["terminal_fg"],
            font=('Consolas', 9),  # Monospace font for terminal
            wrap=tk.NONE,  # No word wrap like terminal
            relief='flat',
            borderwidth=1,
            highlightthickness=1,
            highlightbackground=self.themes[self.current_theme]["border"],
            highlightcolor=self.themes[self.current_theme]["accent"]
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure terminal-style text tags for colored logging
        self.log_text.tag_configure("cmd_input", foreground=self.themes[self.current_theme]["cmd_input"], font=('Consolas', 9, 'bold'))
        self.log_text.tag_configure("cmd_output", foreground=self.themes[self.current_theme]["cmd_output"])
        self.log_text.tag_configure("cmd_error", foreground=self.themes[self.current_theme]["cmd_error"], font=('Consolas', 9, 'bold'))
        self.log_text.tag_configure("cmd_success", foreground=self.themes[self.current_theme]["cmd_success"], font=('Consolas', 9, 'bold'))
        self.log_text.tag_configure("cmd_info", foreground=self.themes[self.current_theme]["cmd_info"])
        self.log_text.tag_configure("cmd_warning", foreground=self.themes[self.current_theme]["cmd_warning"], font=('Consolas', 9, 'bold'))
        self.log_text.tag_configure("cmd_timestamp", foreground=self.themes[self.current_theme]["cmd_timestamp"], font=('Consolas', 8))

        # Legacy tags for compatibility
        self.log_text.tag_configure("info", foreground=self.themes[self.current_theme]["cmd_info"])
        self.log_text.tag_configure("success", foreground=self.themes[self.current_theme]["cmd_success"])
        self.log_text.tag_configure("warning", foreground=self.themes[self.current_theme]["cmd_warning"])
        self.log_text.tag_configure("error", foreground=self.themes[self.current_theme]["cmd_error"])
        self.log_text.tag_configure("timestamp", foreground=self.themes[self.current_theme]["cmd_timestamp"])

        # Welcome message
        welcome_msg = "🚀 Professional Arduino Controller Started - Dark Mode Interface"
        self.log(welcome_msg, "success")

    def log(self, message, level="info"):
        """Terminal-style logging with command/response format"""
        if hasattr(self, 'log_text'):
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]  # Include milliseconds

            # Terminal-style prefixes
            prefixes = {
                "info": "INFO",
                "success": "OK",
                "warning": "WARN",
                "error": "ERR"
            }

            prefix = prefixes.get(level, "LOG")

            # Format like terminal: [timestamp] PREFIX: message
            self.log_text.insert(tk.END, f"[{timestamp}] ", "cmd_timestamp")
            self.log_text.insert(tk.END, f"{prefix}: ", f"cmd_{level}")
            self.log_text.insert(tk.END, f"{message}\n", "cmd_output")

            # Auto-scroll if enabled
            if hasattr(self, 'auto_scroll_var') and self.auto_scroll_var.get():
                self.log_text.see(tk.END)

            # Limit log size (keep last 1000 lines for terminal history)
            lines = self.log_text.get("1.0", tk.END).split('\n')
            if len(lines) > 1000:
                self.log_text.delete("1.0", f"{len(lines)-1000}.0")

    def log_command(self, command):
        """Log a command being sent (terminal input style)"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        self.log_text.insert(tk.END, f"[{timestamp}] ", "cmd_timestamp")
        self.log_text.insert(tk.END, ">>> ", "cmd_input")
        self.log_text.insert(tk.END, f"{command}\n", "cmd_input")
        if hasattr(self, 'auto_scroll_var') and self.auto_scroll_var.get():
            self.log_text.see(tk.END)

    def log_response(self, command, response):
        """Log command response with terminal styling"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]

        if response:
            self.log_text.insert(tk.END, f"[{timestamp}] ", "cmd_timestamp")
            if "ERROR" in response.upper():
                self.log_text.insert(tk.END, "ERR: ", "cmd_error")
                self.log_text.insert(tk.END, f"{response}\n", "cmd_error")
            elif any(word in response.upper() for word in ["OK", "COMPLETE", "STARTED", "STOPPED", "SUCCESS"]):
                self.log_text.insert(tk.END, "<<< ", "cmd_success")
                self.log_text.insert(tk.END, f"{response}\n", "cmd_success")
            else:
                self.log_text.insert(tk.END, "<<< ", "cmd_output")
                self.log_text.insert(tk.END, f"{response}\n", "cmd_output")
        else:
            self.log_text.insert(tk.END, f"[{timestamp}] ", "cmd_timestamp")
            self.log_text.insert(tk.END, "WARN: ", "cmd_warning")
            self.log_text.insert(tk.END, "No response received\n", "cmd_warning")

        if hasattr(self, 'auto_scroll_var') and self.auto_scroll_var.get():
            self.log_text.see(tk.END)

    def clear_log(self):
        """Clear the log"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
            self.log("📝 Log cleared", "info")

    def refresh_all_ui_elements(self):
        """Refresh ALL UI elements with new theme - COMPLETE REFRESH"""
        theme = self.themes[self.current_theme]

        # Update main window
        self.root.configure(bg=theme["bg"])

        # Update header elements
        if hasattr(self, 'title_label'):
            self.title_label.config(
                text=f"🚀 {self.t('app_title')}",
                fg=theme["accent"],
                bg=theme["bg"]
            )

        if hasattr(self, 'subtitle_label'):
            self.subtitle_label.config(
                text=self.t('app_subtitle'),
                fg=theme["fg"],
                bg=theme["bg"]
            )

        # Update terminal toggle button
        if hasattr(self, 'terminal_toggle_btn'):
            text = "📟 Hide Terminal" if self.terminal_visible else "📟 Show Terminal"
            self.terminal_toggle_btn.config(
                text=text,
                bg=theme["button_bg"],
                fg=theme["button_fg"],
                activebackground=theme["button_hover"]
            )

        if hasattr(self, 'fullscreen_btn'):
            self.fullscreen_btn.config(
                bg=theme["surface"],
                fg=theme["fg"],
                activebackground=theme["surface_variant"]
            )

        # Update log text
        if hasattr(self, 'log_text'):
            self.log_text.config(
                bg=theme["terminal_bg"],
                fg=theme["terminal_fg"],
                insertbackground=theme["terminal_cursor"],
                selectbackground=theme["terminal_selection"],
                selectforeground=theme["terminal_fg"]
            )

            # Update log text tags
            self.log_text.tag_configure("cmd_input", foreground=theme["cmd_input"])
            self.log_text.tag_configure("cmd_output", foreground=theme["cmd_output"])
            self.log_text.tag_configure("cmd_error", foreground=theme["cmd_error"])
            self.log_text.tag_configure("cmd_success", foreground=theme["cmd_success"])
            self.log_text.tag_configure("cmd_info", foreground=theme["cmd_info"])
            self.log_text.tag_configure("cmd_warning", foreground=theme["cmd_warning"])
            self.log_text.tag_configure("cmd_timestamp", foreground=theme["cmd_timestamp"])

        # Update all backgrounds
        self.refresh_all_backgrounds()

        # Force a complete redraw
        self.root.update_idletasks()

    def create_modern_connection_frame(self):
        """Create ultra-modern connection frame with responsive design"""
        # Create main connection card with modern styling
        conn_frame = tk.LabelFrame(self.main_container,
                                  text=f"🔌 {self.t('serial_connection')}",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg=self.themes[self.current_theme]["accent"],
                                  bg=self.themes[self.current_theme]["bg"],
                                  labelanchor='nw',
                                  padx=25, pady=20,
                                  relief='flat',
                                  borderwidth=2,
                                  highlightbackground=self.themes[self.current_theme]["border"])
        conn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 25))
        conn_frame.columnconfigure(1, weight=1)
        conn_frame.columnconfigure(3, weight=1)

        # Connection status indicator with modern design
        status_frame = tk.Frame(conn_frame, bg=self.themes[self.current_theme]["bg"])
        status_frame.grid(row=0, column=0, columnspan=6, sticky=(tk.W, tk.E), pady=(0, 20))

        # Enhanced connection indicator
        self.connection_indicator = tk.Canvas(status_frame, width=30, height=30,
                                            highlightthickness=0,
                                            bg=self.themes[self.current_theme]["bg"])
        self.connection_indicator.pack(side=tk.LEFT, padx=(0, 15))

        # Status label with modern styling
        status_text = self.t('connected') if self.is_connected else self.t('disconnected')
        self.status_label = tk.Label(status_frame,
                                    text=f"{'�' if self.is_connected else '🔴'} {status_text}",
                                    font=('Segoe UI', 12, 'bold'),
                                    fg=self.themes[self.current_theme]["success"] if self.is_connected else self.themes[self.current_theme]["error"],
                                    bg=self.themes[self.current_theme]["bg"])
        self.status_label.pack(side=tk.LEFT)

        # Connection controls with modern card design
        controls_frame = tk.Frame(conn_frame, bg=self.themes[self.current_theme]["bg"])
        controls_frame.grid(row=1, column=0, columnspan=6, sticky=(tk.W, tk.E))
        controls_frame.columnconfigure(1, weight=1)
        controls_frame.columnconfigure(3, weight=1)

        # Port selection with modern styling
        port_label = tk.Label(controls_frame,
                             text=f"📍 {self.t('port')}",
                             font=('Segoe UI', 11, 'bold'),
                             fg=self.themes[self.current_theme]["fg"],
                             bg=self.themes[self.current_theme]["bg"])
        port_label.grid(row=0, column=0, padx=(0, 15), sticky=tk.W)

        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(controls_frame, textvariable=self.port_var,
                                      font=('Segoe UI', 11), width=18,
                                      style='Modern.TCombobox')
        self.port_combo.grid(row=0, column=1, padx=(0, 25), sticky=(tk.W, tk.E))

        # Baud rate selection with modern styling
        speed_label = tk.Label(controls_frame,
                              text=f"⚡ {self.t('speed')}",
                              font=('Segoe UI', 11, 'bold'),
                              fg=self.themes[self.current_theme]["fg"],
                              bg=self.themes[self.current_theme]["bg"])
        speed_label.grid(row=0, column=2, padx=(0, 15), sticky=tk.W)

        self.baud_var = tk.StringVar(value="115200")
        baud_combo = ttk.Combobox(controls_frame, textvariable=self.baud_var,
                                 font=('Segoe UI', 11), width=12,
                                 values=["9600", "57600", "115200"],
                                 state="readonly",
                                 style='Modern.TCombobox')
        baud_combo.grid(row=0, column=3, padx=(0, 25), sticky=(tk.W, tk.E))

        # Action buttons with ultra-modern styling
        buttons_frame = tk.Frame(controls_frame, bg=self.themes[self.current_theme]["bg"])
        buttons_frame.grid(row=0, column=4, sticky=tk.E)

        # Refresh button
        self.refresh_btn = tk.Button(buttons_frame,
                                    text=f"🔄 {self.t('refresh')}",
                                    command=self.refresh_ports,
                                    font=('Segoe UI', 10, 'bold'),
                                    bg=self.themes[self.current_theme]["surface"],
                                    fg=self.themes[self.current_theme]["fg"],
                                    activebackground=self.themes[self.current_theme]["surface_variant"],
                                    activeforeground=self.themes[self.current_theme]["fg"],
                                    relief='flat',
                                    borderwidth=0,
                                    padx=18, pady=10,
                                    cursor='hand2')
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 12))

        # Connect button
        connect_text = self.t('disconnect') if self.is_connected else self.t('connect')
        self.connect_btn = tk.Button(buttons_frame,
                                    text=f"{'🔌' if self.is_connected else '🔗'} {connect_text}",
                                    command=self.toggle_connection,
                                    font=('Segoe UI', 10, 'bold'),
                                    bg=self.themes[self.current_theme]["accent"],
                                    fg=self.themes[self.current_theme]["button_fg"],
                                    activebackground=self.themes[self.current_theme]["accent_hover"],
                                    activeforeground=self.themes[self.current_theme]["button_fg"],
                                    relief='flat',
                                    borderwidth=0,
                                    padx=20, pady=10,
                                    cursor='hand2')
        self.connect_btn.pack(side=tk.LEFT)

        # Update connection indicator
        self.update_connection_indicator()

    def create_modern_control_tabs(self):
        """Create ultra-modern control tabs with complete theming"""
        # Ultra-modern notebook with enhanced styling
        self.notebook = ttk.Notebook(self.main_container, style='Modern.TNotebook')
        self.notebook.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 25))

        # Create tabs with ultra-modern design
        self.create_modern_pwm_tab()
        self.create_modern_shooting_tab()
        self.create_modern_stepper_tab()
        self.create_modern_relay_tab()
        self.create_modern_settings_tab()

        # Apply theme to notebook immediately
        self.notebook.configure(style='Modern.TNotebook')

    def create_modern_pwm_tab(self):
        """Create ultra-modern PWM control tab with enhanced visuals"""
        pwm_frame = tk.Frame(self.notebook, bg=self.themes[self.current_theme]["bg"], padx=30, pady=25)
        self.notebook.add(pwm_frame, text=f"🎨 {self.t('pwm_control')}")

        # Configure responsive grid
        pwm_frame.columnconfigure(0, weight=1)

        # PWM channels with ultra-modern design
        self.pwm_vars = []
        self.pwm_scales = []
        self.pwm_labels = []
        self.pwm_canvases = []

        channels = [
            (self.t('red_channel'), "#ef4444", "🔴"),
            (self.t('blue_channel'), "#3b82f6", "🔵"),
            (self.t('green_channel'), "#10b981", "🟢")
        ]

        for i, (name, color_main, _) in enumerate(channels):
            # Ultra-modern card with gradient background
            card_frame = tk.LabelFrame(pwm_frame,
                                      text=f"  {name}  ",
                                      font=('Segoe UI', 12, 'bold'),
                                      fg=self.themes[self.current_theme]["accent"],
                                      bg=self.themes[self.current_theme]["card_bg"],
                                      labelanchor='nw',
                                      padx=25, pady=20,
                                      relief='flat',
                                      borderwidth=2,
                                      highlightbackground=self.themes[self.current_theme]["border_light"])
            card_frame.grid(row=i, column=0, sticky=(tk.W, tk.E), padx=0, pady=15)
            card_frame.columnconfigure(1, weight=1)

            # Enhanced visual indicator with ultra-modern design
            indicator_frame = tk.Frame(card_frame, bg=self.themes[self.current_theme]["card_bg"])
            indicator_frame.grid(row=0, column=0, padx=(0, 25), sticky=tk.W)

            # Create ultra-modern circular color preview with glow
            color_canvas = tk.Canvas(indicator_frame, width=100, height=100,
                                   highlightthickness=0,
                                   bg=self.themes[self.current_theme]["card_bg"])
            color_canvas.pack()

            # Create enhanced gradient circle effect
            self.create_gradient_circle(color_canvas, color_main, self.pwm_values[i])
            self.pwm_canvases.append(color_canvas)

            # Control section
            control_frame = ttk.Frame(card_frame)
            control_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 15))
            control_frame.columnconfigure(0, weight=1)

            # Value variable
            var = tk.IntVar(value=self.pwm_values[i])
            self.pwm_vars.append(var)

            # Modern slider with custom styling
            slider_frame = ttk.Frame(control_frame)
            slider_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
            slider_frame.columnconfigure(0, weight=1)

            scale = tk.Scale(slider_frame, from_=0, to=255, orient=tk.HORIZONTAL,
                           variable=var, command=lambda val, ch=i: self.on_pwm_change(ch, val),
                           bg=self.themes[self.current_theme]["frame_bg"],
                           fg=self.themes[self.current_theme]["fg"],
                           activebackground=color_main,
                           highlightthickness=0,
                           troughcolor=self.themes[self.current_theme]["entry_bg"],
                           font=('Segoe UI', 9, 'bold'),
                           length=300, width=20)
            scale.grid(row=0, column=0, sticky=(tk.W, tk.E))
            self.pwm_scales.append(scale)

            # Value display section
            value_frame = ttk.Frame(control_frame)
            value_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

            # Digital value display
            value_label = ttk.Label(value_frame, text="0 (0.00V)",
                                   font=('Segoe UI', 12, 'bold'))
            value_label.pack(side=tk.LEFT)
            self.pwm_labels.append(value_label)

            # Quick preset buttons with modern styling
            presets_frame = ttk.Frame(card_frame)
            presets_frame.grid(row=0, column=2, sticky=tk.E)

            preset_values = [("OFF", 0), ("25%", 64), ("50%", 128), ("75%", 192), ("MAX", 255)]

            for j, (label, value) in enumerate(preset_values):
                btn = ttk.Button(presets_frame, text=label,
                               command=lambda ch=i, val=value: self.set_pwm_value(ch, val),
                               width=6)
                btn.grid(row=j, column=0, pady=2, sticky=(tk.W, tk.E))

        # Update PWM display
        self.update_pwm_display()

    def create_modern_shooting_tab(self):
        """Create modern shooting control tab"""
        shoot_frame = tk.Frame(self.notebook, bg=self.themes[self.current_theme]["bg"], padx=30, pady=25)
        self.notebook.add(shoot_frame, text=f"⚡ {self.t('pulse_control')}")

        shoot_frame.columnconfigure(0, weight=1)

        # Single shot section with modern design
        single_card = tk.LabelFrame(shoot_frame,
                                   text=f"⚡ {self.t('single_pulse')}",
                                   font=('Segoe UI', 12, 'bold'),
                                   fg=self.themes[self.current_theme]["accent"],
                                   bg=self.themes[self.current_theme]["card_bg"],
                                   padx=25, pady=20)
        single_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        # Large pulse button with visual feedback
        pulse_frame = tk.Frame(single_card, bg=self.themes[self.current_theme]["card_bg"])
        pulse_frame.pack(expand=True)

        self.pulse_btn = tk.Button(pulse_frame,
                                  text=f"🔥 {self.t('send_pulse')}",
                                  command=self.single_shoot,
                                  font=('Segoe UI', 14, 'bold'),
                                  bg=self.themes[self.current_theme]["success"],
                                  fg=self.themes[self.current_theme]["button_fg"],
                                  activebackground=self.themes[self.current_theme]["button_hover"],
                                  activeforeground=self.themes[self.current_theme]["button_fg"],
                                  relief='flat',
                                  borderwidth=0,
                                  padx=30, pady=15,
                                  cursor='hand2')
        self.pulse_btn.pack(pady=10)

        # Enhanced pulse indicator with modern design
        self.pulse_indicator = tk.Canvas(pulse_frame, width=60, height=60,
                                       highlightthickness=0,
                                       bg=self.themes[self.current_theme]["card_bg"])
        self.pulse_indicator.pack(pady=(10, 0))

        # Initialize pulse indicator
        self.create_pulse_indicator(False)

        # Continuous shooting section
        cont_card = tk.LabelFrame(shoot_frame,
                                 text=f"🔄 {self.t('continuous_pulses')}",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg=self.themes[self.current_theme]["accent"],
                                 bg=self.themes[self.current_theme]["card_bg"],
                                 padx=25, pady=20)
        cont_card.grid(row=1, column=0, sticky=(tk.W, tk.E))
        cont_card.columnconfigure(0, weight=1)

        # Rate control with modern slider
        rate_section = tk.Frame(cont_card, bg=self.themes[self.current_theme]["card_bg"])
        rate_section.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        rate_section.columnconfigure(1, weight=1)

        tk.Label(rate_section, text=f"📊 {self.t('pulse_rate')}",
                font=('Segoe UI', 11, 'bold'),
                fg=self.themes[self.current_theme]["fg"],
                bg=self.themes[self.current_theme]["card_bg"]).grid(row=0, column=0, sticky=tk.W, padx=(0, 15))

        self.rate_var = tk.IntVar(value=self.shoot_rate)
        rate_scale = tk.Scale(rate_section, from_=0, to=10, orient=tk.HORIZONTAL,
                             variable=self.rate_var, command=self.on_rate_change,
                             bg=self.themes[self.current_theme]["card_bg"],
                             fg=self.themes[self.current_theme]["fg"],
                             activebackground=self.themes[self.current_theme]["accent"],
                             highlightthickness=0,
                             troughcolor=self.themes[self.current_theme]["entry_bg"],
                             font=('Segoe UI', 10, 'bold'),
                             length=250, width=25)
        rate_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 15))

        self.rate_label = tk.Label(rate_section, text=f"{self.shoot_rate} Hz",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg=self.themes[self.current_theme]["fg"],
                                  bg=self.themes[self.current_theme]["card_bg"])
        self.rate_label.grid(row=0, column=2, sticky=tk.E)

        # Control buttons with modern styling
        controls_section = tk.Frame(cont_card, bg=self.themes[self.current_theme]["card_bg"])
        controls_section.grid(row=1, column=0, pady=(0, 15))

        self.start_shoot_btn = tk.Button(controls_section,
                                        text=f"▶️ {self.t('start_pulses')}",
                                        command=self.start_continuous_shooting,
                                        font=('Segoe UI', 11, 'bold'),
                                        bg=self.themes[self.current_theme]["success"],
                                        fg=self.themes[self.current_theme]["button_fg"],
                                        activebackground=self.themes[self.current_theme]["button_hover"],
                                        activeforeground=self.themes[self.current_theme]["button_fg"],
                                        relief='flat',
                                        borderwidth=0,
                                        padx=20, pady=10,
                                        cursor='hand2')
        self.start_shoot_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_shoot_btn = tk.Button(controls_section,
                                       text=f"⏹️ {self.t('stop_pulses')}",
                                       command=self.stop_continuous_shooting,
                                       font=('Segoe UI', 11, 'bold'),
                                       bg=self.themes[self.current_theme]["error"],
                                       fg=self.themes[self.current_theme]["button_fg"],
                                       activebackground=self.themes[self.current_theme]["warning"],
                                       activeforeground=self.themes[self.current_theme]["button_fg"],
                                       relief='flat',
                                       borderwidth=0,
                                       padx=20, pady=10,
                                       cursor='hand2')
        self.stop_shoot_btn.pack(side=tk.LEFT)

        # Status display with visual indicator
        status_section = tk.Frame(cont_card, bg=self.themes[self.current_theme]["card_bg"])
        status_section.grid(row=2, column=0, sticky=(tk.W, tk.E))

        self.shoot_status_indicator = tk.Canvas(status_section, width=20, height=20,
                                              highlightthickness=0,
                                              bg=self.themes[self.current_theme]["card_bg"])
        self.shoot_status_indicator.pack(side=tk.LEFT, padx=(0, 10))

        self.shoot_status_label = tk.Label(status_section,
                                          text=f"⏸️ {self.t('status')}: {self.t('stopped')}",
                                          font=('Segoe UI', 11, 'bold'),
                                          fg=self.themes[self.current_theme]["fg"],
                                          bg=self.themes[self.current_theme]["card_bg"])
        self.shoot_status_label.pack(side=tk.LEFT)

        # Update shooting status indicator
        self.update_shooting_indicator()

    def create_modern_stepper_tab(self):
        """Create modern stepper motor control tab"""
        stepper_frame = tk.Frame(self.notebook, bg=self.themes[self.current_theme]["bg"], padx=30, pady=25)
        self.notebook.add(stepper_frame, text=f"🔄 {self.t('stepper_motor')}")

        stepper_frame.columnconfigure(0, weight=1)
        stepper_frame.columnconfigure(1, weight=1)

        # Status display
        status_card = tk.LabelFrame(stepper_frame,
                                   text=f"📊 {self.t('motor_status')}",
                                   font=('Segoe UI', 12, 'bold'),
                                   fg=self.themes[self.current_theme]["accent"],
                                   bg=self.themes[self.current_theme]["card_bg"],
                                   padx=25, pady=20)
        status_card.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        self.stepper_status_label = tk.Label(status_card,
                                            text=f"Angle: 0.0° | Speed: 12 RPM | Mode: IDLE",
                                            font=('Segoe UI', 11, 'bold'),
                                            fg=self.themes[self.current_theme]["fg"],
                                            bg=self.themes[self.current_theme]["card_bg"])
        self.stepper_status_label.pack()

        # Angle control
        angle_card = tk.LabelFrame(stepper_frame,
                                  text=f"🎯 {self.t('angle_control')}",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg=self.themes[self.current_theme]["accent"],
                                  bg=self.themes[self.current_theme]["card_bg"],
                                  padx=25, pady=20)
        angle_card.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10), pady=(0, 10))

        # Angle input
        angle_input_frame = tk.Frame(angle_card, bg=self.themes[self.current_theme]["card_bg"])
        angle_input_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(angle_input_frame,
                text=f"{self.t('target_angle')}:",
                font=('Segoe UI', 10, 'bold'),
                fg=self.themes[self.current_theme]["fg"],
                bg=self.themes[self.current_theme]["card_bg"]).pack(anchor=tk.W)

        self.angle_var = tk.IntVar(value=0)
        angle_entry = tk.Entry(angle_input_frame,
                              textvariable=self.angle_var,
                              width=15,
                              font=('Segoe UI', 12),
                              bg=self.themes[self.current_theme]["entry_bg"],
                              fg=self.themes[self.current_theme]["entry_fg"],
                              insertbackground=self.themes[self.current_theme]["fg"],
                              relief='flat',
                              borderwidth=2)
        angle_entry.pack(pady=(5, 10))

        tk.Button(angle_input_frame,
                 text=f"🎯 {self.t('goto_angle')}",
                 command=self.goto_angle,
                 font=('Segoe UI', 10, 'bold'),
                 bg=self.themes[self.current_theme]["accent"],
                 fg=self.themes[self.current_theme]["button_fg"],
                 activebackground=self.themes[self.current_theme]["accent_hover"],
                 activeforeground=self.themes[self.current_theme]["button_fg"],
                 relief='flat',
                 borderwidth=0,
                 padx=15, pady=8,
                 cursor='hand2').pack()

        # Speed control
        speed_frame = tk.Frame(angle_card, bg=self.themes[self.current_theme]["card_bg"])
        speed_frame.pack(fill=tk.X, pady=(10, 0))

        tk.Label(speed_frame,
                text=f"{self.t('speed_rpm')}:",
                font=('Segoe UI', 10, 'bold'),
                fg=self.themes[self.current_theme]["fg"],
                bg=self.themes[self.current_theme]["card_bg"]).pack(anchor=tk.W)

        self.speed_var = tk.IntVar(value=self.stepper_data["speed"])
        speed_scale = tk.Scale(speed_frame, from_=1, to=20, orient=tk.HORIZONTAL,
                              variable=self.speed_var, command=self.on_speed_change,
                              bg=self.themes[self.current_theme]["card_bg"],
                              fg=self.themes[self.current_theme]["fg"],
                              activebackground=self.themes[self.current_theme]["accent"],
                              highlightthickness=0,
                              troughcolor=self.themes[self.current_theme]["entry_bg"],
                              font=('Segoe UI', 10), length=200)
        speed_scale.pack(pady=(5, 0))

        self.speed_label = tk.Label(speed_frame,
                                   text=f"{self.stepper_data['speed']} RPM",
                                   font=('Segoe UI', 11, 'bold'),
                                   fg=self.themes[self.current_theme]["fg"],
                                   bg=self.themes[self.current_theme]["card_bg"])
        self.speed_label.pack()

        # Movement control
        move_card = tk.LabelFrame(stepper_frame,
                                 text=f"🎮 {self.t('movement_control')}",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg=self.themes[self.current_theme]["accent"],
                                 bg=self.themes[self.current_theme]["card_bg"],
                                 padx=25, pady=20)
        move_card.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0), pady=(0, 10))

        # Movement buttons
        buttons = [
            (f"↻ {self.t('clockwise')}", self.stepper_cw, self.themes[self.current_theme]["success"]),
            (f"↺ {self.t('counter_clockwise')}", self.stepper_ccw, self.themes[self.current_theme]["info"]),
            (f"⏹️ {self.t('stop_motor')}", self.stepper_stop, self.themes[self.current_theme]["error"]),
            (f"🔄 {self.t('reset_position')}", self.stepper_reset, self.themes[self.current_theme]["warning"])
        ]

        for text, command, color in buttons:
            btn = tk.Button(move_card,
                           text=text,
                           command=command,
                           font=('Segoe UI', 10, 'bold'),
                           bg=color,
                           fg=self.themes[self.current_theme]["button_fg"],
                           activebackground=color,
                           activeforeground=self.themes[self.current_theme]["button_fg"],
                           relief='flat',
                           borderwidth=0,
                           padx=15, pady=10,
                           cursor='hand2')
            btn.pack(fill=tk.X, pady=5)

    def create_modern_relay_tab(self):
        """Create modern relay control tab"""
        relay_frame = tk.Frame(self.notebook, bg=self.themes[self.current_theme]["bg"], padx=30, pady=25)
        self.notebook.add(relay_frame, text=f"🔌 {self.t('relay_control')}")

        relay_frame.columnconfigure(0, weight=1)
        relay_frame.columnconfigure(1, weight=1)

        # Right relay
        self.create_modern_relay_control(relay_frame, "RIGHT", f"🔌 {self.t('right_relay')}", 0, 0)

        # Left relay
        self.create_modern_relay_control(relay_frame, "LEFT", f"🔌 {self.t('left_relay')}", 0, 1)

    def create_modern_relay_control(self, parent, relay_id, title, row, col):
        """Create modern relay control card"""
        card = tk.LabelFrame(parent,
                            text=title,
                            font=('Segoe UI', 12, 'bold'),
                            fg=self.themes[self.current_theme]["accent"],
                            bg=self.themes[self.current_theme]["card_bg"],
                            padx=25, pady=20)
        card.grid(row=row, column=col, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)

        # Timer setting
        timer_frame = tk.Frame(card, bg=self.themes[self.current_theme]["card_bg"])
        timer_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(timer_frame,
                text=f"{self.t('timer_seconds')}:",
                font=('Segoe UI', 10, 'bold'),
                fg=self.themes[self.current_theme]["fg"],
                bg=self.themes[self.current_theme]["card_bg"]).pack(anchor=tk.W)

        timer_var = tk.IntVar(value=self.relay_data[relay_id.lower()]["timer"])
        setattr(self, f"timer_{relay_id.lower()}_var", timer_var)

        timer_entry = tk.Entry(timer_frame,
                              textvariable=timer_var,
                              width=15,
                              font=('Segoe UI', 12),
                              bg=self.themes[self.current_theme]["entry_bg"],
                              fg=self.themes[self.current_theme]["entry_fg"],
                              insertbackground=self.themes[self.current_theme]["fg"],
                              relief='flat',
                              borderwidth=2)
        timer_entry.pack(pady=(5, 0))

        # Control buttons
        btn_frame = tk.Frame(card, bg=self.themes[self.current_theme]["card_bg"])
        btn_frame.pack(fill=tk.X, pady=(0, 15))

        on_btn = tk.Button(btn_frame,
                          text=f"🟢 {self.t('turn_on')}",
                          command=lambda: self.relay_on(relay_id),
                          font=('Segoe UI', 10, 'bold'),
                          bg=self.themes[self.current_theme]["success"],
                          fg=self.themes[self.current_theme]["button_fg"],
                          activebackground=self.themes[self.current_theme]["button_hover"],
                          activeforeground=self.themes[self.current_theme]["button_fg"],
                          relief='flat',
                          borderwidth=0,
                          padx=15, pady=8,
                          cursor='hand2')
        on_btn.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)

        off_btn = tk.Button(btn_frame,
                           text=f"🔴 {self.t('turn_off')}",
                           command=lambda: self.relay_off(relay_id),
                           font=('Segoe UI', 10, 'bold'),
                           bg=self.themes[self.current_theme]["error"],
                           fg=self.themes[self.current_theme]["button_fg"],
                           activebackground=self.themes[self.current_theme]["warning"],
                           activeforeground=self.themes[self.current_theme]["button_fg"],
                           relief='flat',
                           borderwidth=0,
                           padx=15, pady=8,
                           cursor='hand2')
        off_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Status display
        status_label = tk.Label(card,
                               text=f"{self.t('status')}: {self.t('stopped')}",
                               font=('Segoe UI', 11, 'bold'),
                               fg=self.themes[self.current_theme]["fg"],
                               bg=self.themes[self.current_theme]["card_bg"])
        status_label.pack()
        setattr(self, f"relay_{relay_id.lower()}_status", status_label)

    def create_modern_settings_tab(self):
        """Create modern settings tab"""
        settings_frame = tk.Frame(self.notebook, bg=self.themes[self.current_theme]["bg"], padx=30, pady=25)
        self.notebook.add(settings_frame, text=f"⚙️ {self.t('settings')}")

        settings_frame.columnconfigure(0, weight=1)

        # Auto refresh
        auto_card = tk.LabelFrame(settings_frame,
                                 text=f"🔄 {self.t('auto_refresh')}",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg=self.themes[self.current_theme]["accent"],
                                 bg=self.themes[self.current_theme]["card_bg"],
                                 padx=25, pady=20)
        auto_card.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))

        self.auto_refresh_var = tk.BooleanVar(value=self.auto_refresh)
        auto_check = tk.Checkbutton(auto_card,
                                   text=self.t('auto_refresh_desc'),
                                   variable=self.auto_refresh_var,
                                   command=self.toggle_auto_refresh,
                                   font=('Segoe UI', 10),
                                   fg=self.themes[self.current_theme]["fg"],
                                   bg=self.themes[self.current_theme]["card_bg"],
                                   activeforeground=self.themes[self.current_theme]["fg"],
                                   activebackground=self.themes[self.current_theme]["card_bg"],
                                   selectcolor=self.themes[self.current_theme]["accent"])
        auto_check.pack(anchor=tk.W)

        tk.Button(auto_card,
                 text=f"🔄 {self.t('manual_refresh')}",
                 command=self.manual_refresh,
                 font=('Segoe UI', 10, 'bold'),
                 bg=self.themes[self.current_theme]["info"],
                 fg=self.themes[self.current_theme]["button_fg"],
                 activebackground=self.themes[self.current_theme]["accent_hover"],
                 activeforeground=self.themes[self.current_theme]["button_fg"],
                 relief='flat',
                 borderwidth=0,
                 padx=20, pady=10,
                 cursor='hand2').pack(pady=(15, 0))

        # Save/Load settings
        save_card = tk.LabelFrame(settings_frame,
                                 text=f"💾 {self.t('save_settings')}",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg=self.themes[self.current_theme]["accent"],
                                 bg=self.themes[self.current_theme]["card_bg"],
                                 padx=25, pady=20)
        save_card.grid(row=1, column=0, sticky=(tk.W, tk.E))

        save_buttons = [
            (f"💾 {self.t('save_to_arduino')}", self.save_arduino_settings, self.themes[self.current_theme]["success"]),
            (f"🔄 {self.t('reset_arduino')}", self.reset_arduino_settings, self.themes[self.current_theme]["warning"])
        ]

        for text, command, color in save_buttons:
            btn = tk.Button(save_card,
                           text=text,
                           command=command,
                           font=('Segoe UI', 10, 'bold'),
                           bg=color,
                           fg=self.themes[self.current_theme]["button_fg"],
                           activebackground=color,
                           activeforeground=self.themes[self.current_theme]["button_fg"],
                           relief='flat',
                           borderwidth=0,
                           padx=20, pady=10,
                           cursor='hand2')
            btn.pack(fill=tk.X, pady=5)

    # Connection and control methods
    def update_connection_indicator(self):
        """Update ultra-modern visual connection indicator"""
        self.connection_indicator.delete("all")

        if self.is_connected:
            # Connected - animated green with glow effect
            # Outer glow ring
            self.connection_indicator.create_oval(2, 2, 28, 28,
                                                fill=self.themes[self.current_theme]["success"],
                                                outline=self.themes[self.current_theme]["accent"],
                                                width=3)
            # Inner bright circle
            self.connection_indicator.create_oval(8, 8, 22, 22,
                                                fill=self.themes[self.current_theme]["accent"],
                                                outline="")
            # Center pulse dot
            self.connection_indicator.create_oval(12, 12, 18, 18,
                                                fill="#ffffff",
                                                outline="")

            # Update status text
            status_text = self.t('connected')
            self.status_label.config(
                text=f"🟢 {status_text}",
                fg=self.themes[self.current_theme]["success"]
            )
        else:
            # Disconnected - red indicator
            self.connection_indicator.create_oval(6, 6, 24, 24,
                                                fill=self.themes[self.current_theme]["error"],
                                                outline=self.themes[self.current_theme]["border"],
                                                width=2)

            # Update status text
            status_text = self.t('disconnected')
            self.status_label.config(
                text=f"🔴 {status_text}",
                fg=self.themes[self.current_theme]["error"]
            )

    def refresh_ports(self):
        """Refresh available serial ports"""
        try:
            ports = [port.device for port in serial.tools.list_ports.comports()]
            self.port_combo['values'] = ports
            if ports and not self.port_var.get():
                self.port_var.set(ports[0])
            self.log(f"Found {len(ports)} serial ports", "info")
        except Exception as e:
            self.log(f"Error refreshing ports: {str(e)}", "error")

    def toggle_connection(self):
        """Toggle serial connection"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()

    def connect(self):
        """Connect to Arduino with modern feedback using threading"""
        def connect_thread():
            try:
                port = self.port_var.get()
                baud = int(self.baud_var.get())

                if not port:
                    self.root.after(0, lambda: messagebox.showerror("Error", "Please select a port"))
                    return

                self.root.after(0, lambda: self.log(f"🔄 Connecting to {port} at {baud} baud...", "info"))

                # Disable connect button during connection
                self.root.after(0, lambda: self.connect_btn.config(state='disabled', text="🔄 Connecting..."))

                with self.connection_lock:
                    self.serial_connection = serial.Serial(port, baud, timeout=1)
                    time.sleep(2)  # Wait for Arduino to initialize

                    self.is_connected = True

                # Update UI in main thread
                self.root.after(0, self.on_connection_success)

            except Exception as e:
                # Update UI in main thread
                self.root.after(0, lambda: self.on_connection_error(str(e)))

        # Start connection in background thread
        threading.Thread(target=connect_thread, daemon=True).start()

    def on_connection_success(self):
        """Handle successful connection in main thread"""
        self.connect_btn.config(state='normal', text=f"🔌 {self.t('disconnect')}")
        self.update_connection_indicator()
        self.log(f"✅ Successfully connected to {self.port_var.get()}", "success")

        # Get initial status
        self.manual_refresh()

    def on_connection_error(self, error_msg):
        """Handle connection error in main thread"""
        self.connect_btn.config(state='normal', text=f"🔗 {self.t('connect')}")
        messagebox.showerror("Connection Error", f"Failed to connect: {error_msg}")
        self.log(f"❌ Connection failed: {error_msg}", "error")

    def disconnect(self):
        """Disconnect from Arduino with modern feedback"""
        with self.connection_lock:
            if self.serial_connection:
                self.serial_connection.close()
                self.serial_connection = None

            self.is_connected = False

        self.connect_btn.config(text=f"🔗 {self.t('connect')}")
        self.update_connection_indicator()
        self.log("🔌 Disconnected", "warning")

    def send_command(self, command, callback=None):
        """Send command to Arduino using threading (non-blocking)"""
        if not self.is_connected:
            self.log("Not connected to Arduino", "warning")
            return None

        # Log the command being sent
        self.log_command(command)

        # Add command to queue for background thread
        self.command_queue.put((command, callback))
        return True

    # PWM Control Methods
    def create_gradient_circle(self, canvas, color, intensity):
        """Create a modern gradient circle for PWM visualization"""
        canvas.delete("all")

        # Calculate color intensity
        intensity = max(0, min(1, intensity / 255.0))

        # Parse color
        if color.startswith('#'):
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)
        else:
            r, g, b = 128, 128, 128  # Default gray

        # Apply intensity
        r = int(r * intensity)
        g = int(g * intensity)
        b = int(b * intensity)

        # Create gradient effect with multiple circles
        center_x, center_y = 40, 40
        max_radius = 35

        for i in range(max_radius, 0, -2):
            # Calculate alpha for gradient effect
            alpha = (max_radius - i) / max_radius

            # Adjust color for gradient
            grad_r = int(r + (255 - r) * alpha * 0.3)
            grad_g = int(g + (255 - g) * alpha * 0.3)
            grad_b = int(b + (255 - b) * alpha * 0.3)

            grad_color = f"#{grad_r:02x}{grad_g:02x}{grad_b:02x}"

            # Draw circle
            x1, y1 = center_x - i, center_y - i
            x2, y2 = center_x + i, center_y + i
            canvas.create_oval(x1, y1, x2, y2, fill=grad_color, outline="")

        # Add intensity text
        if intensity > 0.1:
            text_color = "white" if intensity > 0.5 else "black"
            canvas.create_text(center_x, center_y, text=f"{int(intensity * 255)}",
                             fill=text_color, font=('Segoe UI', 12, 'bold'))

        # Add glow effect for high intensity
        if intensity > 0.7:
            glow_color = f"#{min(255, r + 50):02x}{min(255, g + 50):02x}{min(255, b + 50):02x}"
            canvas.create_oval(center_x - max_radius - 5, center_y - max_radius - 5,
                             center_x + max_radius + 5, center_y + max_radius + 5,
                             outline=glow_color, width=2)

    def on_pwm_change(self, channel, value):
        """Handle PWM value change with visual feedback"""
        value = int(float(value))
        self.pwm_values[channel] = value

        # Update display
        voltage = (value / 255.0) * 5.0
        self.pwm_labels[channel].config(text=f"{value} ({voltage:.2f}V)")

        # Update color preview with intensity
        self.update_pwm_color_preview(channel, value)

        # Send to Arduino
        self.send_command(f"SET_PWM,{channel},{value}")

    def update_pwm_color_preview(self, channel, value):
        """Update color preview canvas with modern gradient effect"""
        if hasattr(self, 'pwm_canvases') and channel < len(self.pwm_canvases):
            canvas = self.pwm_canvases[channel]

            # Base colors for each channel (modern palette)
            base_colors = [
                "#ef4444",  # Modern red
                "#3b82f6",  # Modern blue
                "#10b981"   # Modern green
            ]

            color = base_colors[channel]

            # Create gradient circle with intensity
            self.create_gradient_circle(canvas, color, value)

    def set_pwm_value(self, channel, value):
        """Set PWM value directly"""
        self.pwm_vars[channel].set(value)
        self.on_pwm_change(channel, value)

    def update_pwm_display(self):
        """Update PWM display values with modern visual effects"""
        for i in range(3):
            value = self.pwm_values[i]
            voltage = (value / 255.0) * 5.0
            percentage = (value / 255.0) * 100

            # Enhanced display with percentage
            self.pwm_labels[i].config(text=f"{value} ({voltage:.2f}V) - {percentage:.0f}%")

            # Update gradient circle
            if hasattr(self, 'pwm_canvases'):
                self.update_pwm_color_preview(i, value)

    # Shooting/Pulse Control Methods
    def create_pulse_indicator(self, active=False):
        """Create modern pulse indicator"""
        self.pulse_indicator.delete("all")

        if active:
            # Active pulse with glow effect
            # Outer glow
            self.pulse_indicator.create_oval(5, 5, 55, 55,
                                           fill=self.themes[self.current_theme]["accent"],
                                           outline=self.themes[self.current_theme]["accent"],
                                           width=3)

            # Inner bright circle
            self.pulse_indicator.create_oval(15, 15, 45, 45,
                                           fill=self.themes[self.current_theme]["success"],
                                           outline="")

            # Center dot
            self.pulse_indicator.create_oval(25, 25, 35, 35,
                                           fill="#ffffff",
                                           outline="")
        else:
            # Inactive state
            self.pulse_indicator.create_oval(20, 20, 40, 40,
                                           fill=self.themes[self.current_theme]["entry_bg"],
                                           outline=self.themes[self.current_theme]["border"],
                                           width=2)

    def flash_pulse_indicator(self):
        """Enhanced flash pulse indicator with animation"""
        def animate_pulse(step=0):
            if step < 6:  # 6 animation steps
                self.create_pulse_indicator(step % 2 == 0)  # Alternate between active/inactive
                self.pulse_animation_id = self.root.after(100, lambda: animate_pulse(step + 1))
            else:
                self.create_pulse_indicator(False)  # Return to inactive state

        animate_pulse()

    def single_shoot(self):
        """Send single pulse with visual feedback"""
        self.send_command("SINGLE_SHOOT")
        # Flash visual indicator
        self.flash_pulse_indicator()

    def on_rate_change(self, value):
        """Handle shooting rate change"""
        self.shoot_rate = int(float(value))
        self.rate_label.config(text=f"{self.shoot_rate} Hz")
        self.send_command(f"SET_RATE,{self.shoot_rate}")
        # Update status if shooting is active
        if self.continuous_shooting:
            self.update_shooting_indicator()

    def start_continuous_shooting(self):
        """Start continuous shooting with visual feedback"""
        response = self.send_command("START_SHOOT")
        self.continuous_shooting = True
        self.update_shooting_indicator()
        # Disable start button, enable stop button
        self.start_shoot_btn.config(state='disabled')
        self.stop_shoot_btn.config(state='normal')

    def stop_continuous_shooting(self):
        """Stop continuous shooting with visual feedback"""
        response = self.send_command("STOP_SHOOT")
        self.continuous_shooting = False
        self.update_shooting_indicator()
        # Enable start button, disable stop button
        self.start_shoot_btn.config(state='normal')
        self.stop_shoot_btn.config(state='disabled')

    def update_shooting_indicator(self):
        """Update shooting status visual indicator"""
        self.shoot_status_indicator.delete("all")
        if self.continuous_shooting:
            # Animated green circle for active shooting
            self.shoot_status_indicator.create_oval(2, 2, 18, 18,
                                                   fill=self.themes[self.current_theme]["success"],
                                                   outline="")
            self.shoot_status_label.config(text=f"🟢 {self.t('active')}: {self.shoot_rate} Hz")
        else:
            # Gray circle for stopped
            self.shoot_status_indicator.create_oval(2, 2, 18, 18,
                                                   fill=self.themes[self.current_theme]["entry_bg"],
                                                   outline="")
            self.shoot_status_label.config(text=f"⏸️ {self.t('status')}: {self.t('stopped')}")

    # Stepper Motor Control Methods
    def goto_angle(self):
        """Move stepper to specific angle"""
        angle = self.angle_var.get()
        if 0 <= angle < 360:
            self.send_command(f"STEPPER_ANGLE,{angle}")
            self.log(f"Moving stepper to {angle}°", "info")
        else:
            messagebox.showerror("Error", "Angle must be between 0 and 359")

    def on_speed_change(self, value):
        """Handle stepper speed change"""
        speed = int(float(value))
        self.stepper_data["speed"] = speed
        self.speed_label.config(text=f"{speed} RPM")
        self.send_command(f"STEPPER_SPEED,{speed}")

    def stepper_cw(self):
        """Start clockwise rotation"""
        self.send_command("STEPPER_CW")
        self.log("Stepper rotating clockwise", "info")

    def stepper_ccw(self):
        """Start counter-clockwise rotation"""
        self.send_command("STEPPER_CCW")
        self.log("Stepper rotating counter-clockwise", "info")

    def stepper_stop(self):
        """Stop stepper motor"""
        self.send_command("STEPPER_STOP")
        self.log("Stepper motor stopped", "info")

    def stepper_reset(self):
        """Reset stepper position"""
        self.send_command("STEPPER_RESET")
        self.log("Stepper position reset", "info")

    # Relay Control Methods
    def relay_on(self, relay_id):
        """Turn relay on"""
        timer_var = getattr(self, f"timer_{relay_id.lower()}_var")
        timer = timer_var.get()
        self.send_command(f"RELAY_{relay_id}_ON,{timer}")
        self.log(f"Relay {relay_id} turned ON (timer: {timer}s)", "info")

    def relay_off(self, relay_id):
        """Turn relay off"""
        self.send_command(f"RELAY_{relay_id}_OFF")
        self.log(f"Relay {relay_id} turned OFF", "info")

    # Status and Settings Methods
    def manual_refresh(self):
        """Manually refresh all status"""
        if not self.is_connected:
            self.log("Not connected - cannot refresh status", "warning")
            return

        self.log("Refreshing device status...", "info")

        # Get PWM values
        self.send_command("GET_PWM", self.handle_pwm_response)

        # Get stepper status
        self.send_command("GET_STEPPER", self.handle_stepper_response)

        # Get relay status
        self.send_command("GET_RELAY", self.handle_relay_response)

    def handle_pwm_response(self, response):
        """Handle PWM status response"""
        if response and "PWM_VALUES:" in response:
            try:
                values = response.split(":")[1].split(",")
                for i, val in enumerate(values[:3]):
                    self.pwm_values[i] = int(val)
                    self.pwm_vars[i].set(int(val))
                self.update_pwm_display()
                self.log("PWM values updated", "success")
            except Exception as e:
                self.log(f"Error parsing PWM response: {e}", "error")

    def handle_stepper_response(self, response):
        """Handle stepper status response"""
        if response and "STEPPER_STATUS:" in response:
            try:
                data = response.split(":")[1].split(",")
                if len(data) >= 3:
                    self.stepper_data["angle"] = float(data[0])
                    self.stepper_data["speed"] = int(data[1])
                    self.stepper_data["mode"] = data[2]
                    self.stepper_status_label.config(
                        text=f"Angle: {self.stepper_data['angle']:.1f}° | "
                             f"Speed: {self.stepper_data['speed']} RPM | "
                             f"Mode: {self.stepper_data['mode']}")
                    self.log("Stepper status updated", "success")
            except Exception as e:
                self.log(f"Error parsing stepper response: {e}", "error")

    def handle_relay_response(self, response):
        """Handle relay status response"""
        if response and "RELAY_STATUS:" in response:
            try:
                data = response.split(":")[1].split(",")
                if len(data) >= 6:
                    # Right relay
                    self.relay_data["right"]["active"] = data[0] == "1"
                    self.relay_data["right"]["timer"] = int(data[1])
                    self.relay_data["right"]["remaining"] = int(data[2])

                    # Left relay
                    self.relay_data["left"]["active"] = data[3] == "1"
                    self.relay_data["left"]["timer"] = int(data[4])
                    self.relay_data["left"]["remaining"] = int(data[5])

                    # Update display
                    self.update_relay_display()
                    self.log("Relay status updated", "success")
            except Exception as e:
                self.log(f"Error parsing relay response: {e}", "error")

    def update_relay_display(self):
        """Update relay status display"""
        for relay_id in ["right", "left"]:
            data = self.relay_data[relay_id]
            status_label = getattr(self, f"relay_{relay_id}_status")

            if data["active"]:
                if data["remaining"] > 0:
                    status_text = f"{self.t('status')}: {self.t('active')} (remaining: {data['remaining']/1000:.1f}s)"
                else:
                    status_text = f"{self.t('status')}: {self.t('active')} (manual)"
            else:
                status_text = f"{self.t('status')}: {self.t('stopped')}"

            status_label.config(text=status_text)

    def toggle_auto_refresh(self):
        """Toggle auto refresh"""
        self.auto_refresh = self.auto_refresh_var.get()
        if self.auto_refresh:
            self.log("Auto refresh enabled", "info")
        else:
            self.log("Auto refresh disabled", "info")

    def save_arduino_settings(self):
        """Save settings to Arduino EEPROM"""
        if not self.is_connected:
            messagebox.showerror("Error", "Not connected to Arduino")
            return

        self.send_command("SAVE", self.handle_save_response)

    def handle_save_response(self, response):
        """Handle save settings response"""
        if response and "SAVED" in response:
            messagebox.showinfo("Success", "Settings saved to Arduino")
            self.log("Settings saved to Arduino EEPROM", "success")
        else:
            messagebox.showerror("Error", "Failed to save settings")
            self.log("Failed to save settings", "error")

    def reset_arduino_settings(self):
        """Reset Arduino to default settings"""
        if not self.is_connected:
            messagebox.showerror("Error", "Not connected to Arduino")
            return

        if messagebox.askyesno("Confirm", "Reset all settings to default values?"):
            self.send_command("RESET", self.handle_reset_response)

    def handle_reset_response(self, response):
        """Handle reset settings response"""
        if response and "COMPLETE" in response:
            messagebox.showinfo("Success", "Settings reset to defaults")
            self.log("Arduino settings reset to defaults", "success")
            self.manual_refresh()
        else:
            messagebox.showerror("Error", "Failed to reset settings")
            self.log("Failed to reset settings", "error")

    def auto_refresh_timer(self):
        """Auto refresh timer"""
        if self.auto_refresh and self.is_connected:
            self.manual_refresh()
        self.root.after(2000, self.auto_refresh_timer)  # Every 2 seconds

    def load_settings(self):
        """Load GUI settings from file"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.auto_refresh = settings.get('auto_refresh', False)
                    self.current_theme = settings.get('theme', 'dark')
                    self.current_language = settings.get('language', 'en')
                    self.terminal_visible = settings.get('terminal_visible', True)
        except Exception:
            self.auto_refresh = False
            self.current_theme = 'dark'
            self.current_language = 'en'
            self.terminal_visible = True

    def save_settings(self):
        """Save GUI settings to file"""
        try:
            settings = {
                'auto_refresh': self.auto_refresh,
                'theme': self.current_theme,
                'language': self.current_language,
                'terminal_visible': self.terminal_visible
            }
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception:
            pass

    def on_closing(self):
        """Handle window closing with proper cleanup"""
        # Stop all threads and animations
        self.running = False

        # Cancel any pending animations
        if self.pulse_animation_id:
            self.root.after_cancel(self.pulse_animation_id)
        if self.connection_pulse_id:
            self.root.after_cancel(self.connection_pulse_id)

        # Save settings
        self.save_settings()

        # Disconnect if connected
        if self.is_connected and hasattr(self, 'serial_connection') and self.serial_connection:
            try:
                self.serial_connection.close()
            except:
                pass

        # Wait a moment for threads to finish
        time.sleep(0.1)

        self.root.destroy()

def main():
    root = tk.Tk()
    app = ModernArduinoController(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
