# Arduino Controller Pro - Updated Usage Guide

## 🚀 Real Arduino Communication with Enhanced Features

### Updated File Structure
```
arduino-controller/
├── arduino_gui.py                    # Professional Dark Mode GUI
├── src/arduino_controller.cpp        # Enhanced Arduino firmware
├── UPDATED_USAGE_GUIDE.md            # This guide
└── DARK_MODE_IMPROVEMENTS.md         # Design documentation
```

## 🔌 Hardware Setup

### Pin Configuration (Updated)
```cpp
// PWM Control Pins
const int RED_LED_PIN = 9;      // Red LED/Channel
const int GREEN_LED_PIN = 5;    // Green LED/Channel  
const int BLUE_LED_PIN = 6;     // Blue LED/Channel
const int PULSE_PIN = 8;        // Pulse/Shooting output

// LED Matrix Pin (Testing)
const int LED_MATRIX_PIN = 13;  // Built-in LED for simulation

// Relay Control Pins
const int RELAY_RIGHT_PIN = 10; // Right relay
const int RELAY_LEFT_PIN = 11;  // Left relay

// Stepper Motor Pins (28BYJ-48)
const int STEPPER_PIN_IN1 = 2;
const int STEPPER_PIN_IN2 = 3;
const int STEPPER_PIN_IN3 = 4;
const int STEPPER_PIN_IN4 = 7;
```

## 🎯 Enhanced Serial Communication Protocol

### Connection Commands
```
PING                    -> PONG
INFO                    -> Arduino Controller v2.0 - Professional Dark Mode Compatible
```

### PWM Commands (Real-time Control)
```
SET_PWM,0,255          -> PWM_OK          (Red channel to max)
SET_PWM,1,128          -> PWM_OK          (Green channel to half)
SET_PWM,2,64           -> PWM_OK          (Blue channel to quarter)
GET_PWM                -> PWM_VALUES:255,128,64
```

### LED Matrix Commands (NEW!)
```
LED_MATRIX_ON          -> LED_MATRIX_ON_OK
LED_MATRIX_OFF         -> LED_MATRIX_OFF_OK
LED_MATRIX_PATTERN,1   -> LED_MATRIX_PATTERN_OK    (Pattern 1)
LED_MATRIX_PATTERN,2   -> LED_MATRIX_PATTERN_OK    (Pattern 2)
LED_MATRIX_BLINK       -> LED_MATRIX_BLINK_OK      (Blink test)
```

### Shooting/Pulse Commands
```
SINGLE_SHOOT           -> SHOOT_OK
START_SHOOT            -> CONTINUOUS_SHOOT_STARTED
STOP_SHOOT             -> CONTINUOUS_SHOOT_STOPPED
SET_RATE,5             -> RATE_OK         (5 Hz pulse rate)
```

### Stepper Motor Commands (Enhanced)
```
STEPPER_CW             -> STEPPER_CW_OK
STEPPER_CCW            -> STEPPER_CCW_OK
STEPPER_STOP           -> STEPPER_STOP_OK
STEPPER_RESET          -> STEPPER_RESET_OK
STEPPER_ANGLE,180      -> STEPPER_ANGLE_OK
STEPPER_SPEED,15       -> STEPPER_SPEED_OK
GET_STEPPER            -> STEPPER_STATUS:180.0,15,IDLE
```

### Relay Commands (Timer Support)
```
RELAY_RIGHT_ON,5       -> RELAY_RIGHT_ON_OK    (5 second auto-off)
RELAY_RIGHT_ON,0       -> RELAY_RIGHT_ON_OK    (Manual mode)
RELAY_RIGHT_OFF        -> RELAY_RIGHT_OFF_OK
RELAY_LEFT_ON,3        -> RELAY_LEFT_ON_OK     (3 second auto-off)
RELAY_LEFT_OFF         -> RELAY_LEFT_OFF_OK
GET_RELAY              -> RELAY_STATUS:1,5,4500,0,3,0
```

### System Commands
```
SAVE                   -> SETTINGS_SAVED       (Save to EEPROM)
RESET                  -> RESET_COMPLETE       (Reset all to defaults)
```

## 🎨 GUI Features (Professional Dark Mode)

### 1. **Connection Panel**
- **Enhanced Port Detection**: Automatic COM port discovery
- **Baud Rate Selection**: 9600, 57600, 115200 (optimized for 115200)
- **Real-time Status**: Animated connection indicator
- **Auto-Recovery**: Handles connection loss gracefully

### 2. **PWM Control Tab**
- **3-Channel RGB Control**: Real-time LED brightness control
- **Visual Feedback**: Gradient color previews with intensity
- **Preset Buttons**: Quick access (OFF, 25%, 50%, 75%, MAX)
- **Live Voltage Display**: Shows actual output voltage (0-5V)
- **Instant Response**: Changes applied immediately to hardware

### 3. **Pulse Control Tab**
- **Single Pulse Mode**: One-time pulse generation
- **Continuous Mode**: Adjustable frequency 1-10 Hz
- **Visual Pulse Indicator**: Animated feedback
- **Rate Control**: Real-time frequency adjustment
- **Status Monitoring**: Live shooting state display

### 4. **Stepper Motor Tab**
- **Precision Angle Control**: 0-359° positioning
- **Speed Adjustment**: 1-20 RPM with live feedback
- **Manual Movement**: Clockwise/Counter-clockwise buttons
- **Position Reset**: Return to zero position
- **Real-time Status**: Current angle, speed, and mode

### 5. **Relay Control Tab**
- **Dual Independent Control**: Left and right relays
- **Timer Mode**: Automatic shutoff (1-3600 seconds)
- **Manual Mode**: Continuous operation (timer = 0)
- **Safety Features**: Prevents accidental long activation
- **Status Monitoring**: Real-time relay state and countdown

### 6. **Test & Diagnostics Tab** (Enhanced)
- **Connection Testing**: Ping Arduino for response verification
- **System Information**: Get Arduino firmware details
- **LED Matrix Testing**: All patterns and blink tests
- **Component Testing**: Individual system verification
- **Full System Diagnostic**: Comprehensive automated testing

### 7. **Settings Tab**
- **Auto Refresh**: Automatic status updates every 2 seconds
- **Manual Refresh**: On-demand status synchronization
- **EEPROM Storage**: Save settings to Arduino memory
- **Factory Reset**: Restore all default values

## 🔧 Professional Terminal (Fixed Bottom)

### Real-time Communication Display
- **Command Logging**: Sent commands in green
- **Response Logging**: Arduino responses in white
- **Error Highlighting**: Errors in red with timestamps
- **Success Confirmation**: Successful operations in green
- **Millisecond Timestamps**: Precise timing information

### Terminal Controls
- **Hide/Show Toggle**: Maximize workspace when needed
- **Clear Function**: Remove all log entries
- **Auto-scroll**: Optional automatic scrolling to latest
- **History Management**: Maintains last 1000 entries
- **Fixed Position**: Always accessible at bottom

## 🚀 Quick Start Guide

### Step 1: Upload Arduino Code
1. Open Arduino IDE or PlatformIO
2. Load `src/arduino_controller.cpp`
3. Select your Arduino board and COM port
4. Upload the code (115200 baud rate)
5. Open Serial Monitor and test with "PING" command

### Step 2: Run Python GUI
```bash
# Install dependencies
pip install pyserial

# Run the application
python arduino_gui.py
```

### Step 3: Connect and Test
1. **Select Port**: Choose your Arduino's COM port
2. **Set Baud Rate**: Select 115200 (recommended)
3. **Connect**: Click connect button
4. **Test Connection**: Go to "Test & Diagnostics" tab
5. **Ping Test**: Click "Ping Arduino" - should show "PONG"

### Step 4: Test LED Matrix
1. **Basic Control**: Click "Turn ON LED Matrix"
2. **Pattern Test**: Try "LED Matrix Pattern 1" and "Pattern 2"
3. **Blink Test**: Click "LED Matrix Blink Test"
4. **Monitor Terminal**: Watch for "LED_MATRIX_*_OK" responses

### Step 5: Test Other Components
1. **PWM**: Move RGB sliders and observe LED changes
2. **Pulse**: Try single pulse and continuous modes
3. **Stepper**: Test angle control and manual movement
4. **Relays**: Use timer mode for safety testing

## 🛠️ Troubleshooting

### Connection Issues
- **Port Not Found**: Refresh ports or check USB connection
- **Permission Denied**: Run as administrator
- **No Response**: Verify 115200 baud rate and Arduino code
- **Connection Lost**: Check USB cable quality

### LED Matrix Issues
- **No Response**: Verify pin 13 connection (built-in LED)
- **Pattern Not Working**: Check serial monitor for responses
- **Blink Test Fails**: Ensure Arduino code is properly uploaded

### Hardware Debugging
- **PWM Not Working**: Check pin connections (5, 6, 9)
- **Stepper Not Moving**: Verify 28BYJ-48 wiring and power
- **Relays Not Switching**: Check relay module compatibility
- **Pulse Not Working**: Verify pin 8 connection

## 📊 Performance Optimization

### Best Practices
- **Use 115200 Baud**: Optimal for real-time communication
- **Enable Auto Refresh**: For live status monitoring
- **Monitor Terminal**: Watch for error messages
- **Test Incrementally**: Start with simple commands
- **Save Settings**: Store working configurations

### Communication Tips
- **Wait for Responses**: Each command returns confirmation
- **Check Terminal**: Monitor all communication
- **Use Diagnostics**: Regular system health checks
- **Handle Errors**: Watch for "ERROR:" responses

## 🎉 Ready for Professional Use!

Your Arduino Controller Pro now features:
- ✅ **Real Arduino Communication** with enhanced protocol
- ✅ **LED Matrix Control** with pattern testing
- ✅ **Professional Dark Mode** interface
- ✅ **Comprehensive Testing** capabilities
- ✅ **EEPROM Settings** storage
- ✅ **Responsive Design** for all screen sizes
- ✅ **Terminal-style Logging** with color coding

**Start controlling your Arduino hardware like a professional!** 🚀

### Next Steps
1. Connect your hardware components
2. Upload the Arduino code
3. Test each component individually
4. Use the full system diagnostic
5. Save your working configuration
6. Enjoy professional Arduino control!
