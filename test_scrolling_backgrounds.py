#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Scrolling and Background Fixes
اختبار التمرير وإصلاح الخلفيات
"""

import tkinter as tk
from tkinter import ttk
import time

class ScrollingBackgroundTest:
    """Test scrolling and complete background theming"""
    
    def __init__(self, root):
        self.root = root
        
        # Theme settings
        self.current_theme = "dark"
        self.current_language = "en"
        
        self.setup_themes()
        self.setup_translations()
        self.setup_window()
        self.create_scrollable_interface()
    
    def setup_themes(self):
        """Setup complete themes"""
        self.themes = {
            "dark": {
                "bg": "#0f0f23",
                "fg": "#f8fafc",
                "card_bg": "#312e81",
                "accent": "#06b6d4",
                "accent_hover": "#0891b2",
                "button_bg": "#7c3aed",
                "button_fg": "#ffffff",
                "button_hover": "#8b5cf6",
                "success": "#10b981",
                "error": "#ef4444",
                "surface": "#262626",
                "surface_variant": "#374151",
                "entry_bg": "#1e1b4b",
                "entry_fg": "#f1f5f9"
            },
            "light": {
                "bg": "#f8fafc",
                "fg": "#0f172a",
                "card_bg": "#f1f5f9",
                "accent": "#0ea5e9",
                "accent_hover": "#0284c7",
                "button_bg": "#7c3aed",
                "button_fg": "#ffffff",
                "button_hover": "#8b5cf6",
                "success": "#059669",
                "error": "#ef4444",
                "surface": "#f1f5f9",
                "surface_variant": "#e2e8f0",
                "entry_bg": "#ffffff",
                "entry_fg": "#1e293b"
            }
        }
    
    def setup_translations(self):
        """Setup translations"""
        self.translations = {
            "en": {
                "title": "Scrolling & Background Test",
                "subtitle": "Testing Complete Theme Switching",
                "theme": "Theme",
                "language": "Language",
                "dark_mode": "Dark",
                "light_mode": "Light",
                "section": "Section",
                "content": "This is test content to demonstrate scrolling and background changes.",
                "long_text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
            },
            "ar": {
                "title": "اختبار التمرير والخلفيات",
                "subtitle": "اختبار التبديل الكامل للثيم",
                "theme": "المظهر",
                "language": "اللغة",
                "dark_mode": "مظلم",
                "light_mode": "فاتح",
                "section": "قسم",
                "content": "هذا محتوى تجريبي لإظهار التمرير وتغيير الخلفيات.",
                "long_text": "هذا نص طويل لاختبار التمرير والتأكد من أن كل الخلفيات تتغير بشكل صحيح عند تبديل الثيم. يجب أن تكون كل الخلفيات متناسقة ومتطابقة مع الثيم المختار."
            }
        }
    
    def t(self, key):
        """Get translated text"""
        return self.translations[self.current_language].get(key, key)
    
    def setup_window(self):
        """Setup responsive window"""
        self.root.title(self.t('title'))
        self.root.geometry("1000x700")
        self.root.configure(bg=self.themes[self.current_theme]["bg"])
        
        # Configure grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def create_scrollable_interface(self):
        """Create scrollable interface"""
        # Main canvas for scrolling
        self.main_canvas = tk.Canvas(self.root,
                                    bg=self.themes[self.current_theme]["bg"],
                                    highlightthickness=0)
        self.main_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar
        self.scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=self.main_canvas.yview)
        self.scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.main_canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # Scrollable frame
        self.scrollable_frame = tk.Frame(self.main_canvas, bg=self.themes[self.current_theme]["bg"])
        self.canvas_window = self.main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        
        # Main container
        self.main_container = tk.Frame(self.scrollable_frame,
                                      bg=self.themes[self.current_theme]["bg"],
                                      padx=30, pady=20)
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # Configure scrolling
        self.scrollable_frame.bind("<Configure>", self.on_frame_configure)
        self.main_canvas.bind("<Configure>", self.on_canvas_configure)
        self.main_canvas.bind("<MouseWheel>", self.on_mousewheel)
        self.root.bind("<MouseWheel>", self.on_mousewheel)
        
        # Create content
        self.create_header()
        self.create_content_sections()
    
    def create_header(self):
        """Create header with theme controls"""
        header_frame = tk.Frame(self.main_container, bg=self.themes[self.current_theme]["bg"])
        header_frame.pack(fill=tk.X, pady=(0, 30))
        
        # Title
        self.title_label = tk.Label(header_frame,
                                   text=f"🚀 {self.t('title')}",
                                   font=('Segoe UI', 18, 'bold'),
                                   fg=self.themes[self.current_theme]["accent"],
                                   bg=self.themes[self.current_theme]["bg"])
        self.title_label.pack(side=tk.LEFT)
        
        # Controls
        controls_frame = tk.Frame(header_frame, bg=self.themes[self.current_theme]["bg"])
        controls_frame.pack(side=tk.RIGHT)
        
        # Language button
        self.language_btn = tk.Button(controls_frame,
                                     text=f"🌐 {self.t('language')}",
                                     command=self.toggle_language,
                                     font=('Segoe UI', 10, 'bold'),
                                     bg=self.themes[self.current_theme]["button_bg"],
                                     fg=self.themes[self.current_theme]["button_fg"],
                                     activebackground=self.themes[self.current_theme]["button_hover"],
                                     relief='flat', borderwidth=0,
                                     padx=20, pady=10, cursor='hand2')
        self.language_btn.pack(side=tk.RIGHT, padx=(0, 15))
        
        # Theme button
        theme_text = f"🌙 {self.t('dark_mode')}" if self.current_theme == "light" else f"☀️ {self.t('light_mode')}"
        self.theme_btn = tk.Button(controls_frame,
                                  text=theme_text,
                                  command=self.toggle_theme,
                                  font=('Segoe UI', 10, 'bold'),
                                  bg=self.themes[self.current_theme]["accent"],
                                  fg=self.themes[self.current_theme]["button_fg"],
                                  activebackground=self.themes[self.current_theme]["accent_hover"],
                                  relief='flat', borderwidth=0,
                                  padx=20, pady=10, cursor='hand2')
        self.theme_btn.pack(side=tk.RIGHT)
    
    def create_content_sections(self):
        """Create multiple content sections for scrolling test"""
        for i in range(10):  # Create 10 sections to test scrolling
            section_frame = tk.LabelFrame(self.main_container,
                                         text=f"{self.t('section')} {i+1}",
                                         font=('Segoe UI', 12, 'bold'),
                                         fg=self.themes[self.current_theme]["accent"],
                                         bg=self.themes[self.current_theme]["bg"],
                                         padx=20, pady=15)
            section_frame.pack(fill=tk.X, pady=(0, 20))
            
            # Content in each section
            content_frame = tk.Frame(section_frame, bg=self.themes[self.current_theme]["bg"])
            content_frame.pack(fill=tk.X)
            
            # Text content
            content_label = tk.Label(content_frame,
                                    text=f"{self.t('content')} {i+1}",
                                    font=('Segoe UI', 11),
                                    fg=self.themes[self.current_theme]["fg"],
                                    bg=self.themes[self.current_theme]["bg"],
                                    wraplength=800,
                                    justify=tk.LEFT)
            content_label.pack(anchor=tk.W, pady=(0, 10))
            
            # Long text
            long_text_label = tk.Label(content_frame,
                                      text=self.t('long_text'),
                                      font=('Segoe UI', 10),
                                      fg=self.themes[self.current_theme]["fg"],
                                      bg=self.themes[self.current_theme]["bg"],
                                      wraplength=800,
                                      justify=tk.LEFT)
            long_text_label.pack(anchor=tk.W, pady=(0, 10))
            
            # Test entry
            test_entry = tk.Entry(content_frame,
                                 font=('Segoe UI', 10),
                                 bg=self.themes[self.current_theme]["entry_bg"],
                                 fg=self.themes[self.current_theme]["entry_fg"],
                                 insertbackground=self.themes[self.current_theme]["fg"],
                                 relief='flat', borderwidth=2,
                                 width=50)
            test_entry.pack(anchor=tk.W, pady=(0, 10))
            test_entry.insert(0, f"Test entry {i+1}")
            
            # Store references for theme updates
            if not hasattr(self, 'content_widgets'):
                self.content_widgets = []
            
            self.content_widgets.extend([
                section_frame, content_frame, content_label, long_text_label, test_entry
            ])
    
    def on_frame_configure(self, event):
        """Configure scroll region"""
        self.main_canvas.configure(scrollregion=self.main_canvas.bbox("all"))
    
    def on_canvas_configure(self, event):
        """Configure canvas window width"""
        canvas_width = event.width
        self.main_canvas.itemconfig(self.canvas_window, width=canvas_width)
    
    def on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.main_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def toggle_theme(self):
        """Toggle theme with complete background refresh"""
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
        self.refresh_all_backgrounds()
        print(f"Theme changed to: {self.current_theme}")
    
    def toggle_language(self):
        """Toggle language with complete refresh"""
        self.current_language = "ar" if self.current_language == "en" else "en"
        self.refresh_all_backgrounds()
        print(f"Language changed to: {self.current_language}")
    
    def refresh_all_backgrounds(self):
        """Refresh ALL backgrounds and colors"""
        theme = self.themes[self.current_theme]
        
        # Update window
        self.root.configure(bg=theme["bg"])
        
        # Update canvas and scrollable components
        self.main_canvas.configure(bg=theme["bg"])
        self.scrollable_frame.configure(bg=theme["bg"])
        self.main_container.configure(bg=theme["bg"])
        
        # Update header
        self.title_label.configure(text=f"🚀 {self.t('title')}", fg=theme["accent"], bg=theme["bg"])
        
        # Update buttons
        self.language_btn.configure(text=f"🌐 {self.t('language')}", bg=theme["button_bg"], 
                                   fg=theme["button_fg"], activebackground=theme["button_hover"])
        
        theme_text = f"🌙 {self.t('dark_mode')}" if self.current_theme == "light" else f"☀️ {self.t('light_mode')}"
        self.theme_btn.configure(text=theme_text, bg=theme["accent"], 
                                fg=theme["button_fg"], activebackground=theme["accent_hover"])
        
        # Update all content widgets
        if hasattr(self, 'content_widgets'):
            for widget in self.content_widgets:
                try:
                    if isinstance(widget, tk.LabelFrame):
                        widget.configure(fg=theme["accent"], bg=theme["bg"])
                    elif isinstance(widget, tk.Frame):
                        widget.configure(bg=theme["bg"])
                    elif isinstance(widget, tk.Label):
                        widget.configure(fg=theme["fg"], bg=theme["bg"])
                    elif isinstance(widget, tk.Entry):
                        widget.configure(bg=theme["entry_bg"], fg=theme["entry_fg"],
                                       insertbackground=theme["fg"])
                except:
                    pass
        
        # Force complete redraw
        self.root.update_idletasks()
        print("All backgrounds refreshed!")

def main():
    """Main function"""
    root = tk.Tk()
    app = ScrollingBackgroundTest(root)
    
    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() - root.winfo_width()) // 2
    y = (root.winfo_screenheight() - root.winfo_height()) // 2
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
