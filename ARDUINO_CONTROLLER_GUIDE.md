# Arduino Controller Pro - Complete Usage Guide

## 🎯 Real Arduino Communication Setup

### Hardware Requirements
- Arduino Uno/Nano/ESP32 (any compatible board)
- USB cable for serial communication
- Components as per pin configuration

### Pin Configuration
```cpp
// PWM Control Pins
const int PWM_RED = 9;      // Red LED/Channel
const int PWM_BLUE = 6;     // Blue LED/Channel  
const int PWM_GREEN = 5;    // Green LED/Channel

// Relay Control Pins
const int RELAY_RIGHT = 10; // Right relay
const int RELAY_LEFT = 11;  // Left relay

// Stepper Motor Pins (4-wire stepper)
// Pins 2, 3, 4, 7 for stepper motor control

// LED Matrix Pins (MAX7219 or similar)
const int LED_MATRIX_DATA = 8;   // Data pin
const int LED_MATRIX_CLOCK = 12; // Clock pin
const int LED_MATRIX_CS = 13;    // Chip select
```

## 🔌 Serial Communication Protocol

### Command Structure
All commands are ASCII strings terminated with `\r\n`:

#### Connection Commands
```
PING                    -> PONG
INFO                    -> Arduino Controller v1.0 - Ready
```

#### PWM Commands
```
SET_PWM,0,255          -> PWM_OK          (Set Red to max)
SET_PWM,1,128          -> PWM_OK          (Set Blue to half)
SET_PWM,2,0            -> PWM_OK          (Set Green to off)
GET_PWM                -> PWM_VALUES:255,128,0
```

#### Shooting/Pulse Commands
```
SINGLE_SHOOT           -> SHOOT_OK
START_SHOOT            -> CONTINUOUS_SHOOT_STARTED
STOP_SHOOT             -> CONTINUOUS_SHOOT_STOPPED
SET_RATE,5             -> RATE_OK         (5 Hz pulse rate)
```

#### Stepper Motor Commands
```
STEPPER_CW             -> STEPPER_CW_OK
STEPPER_CCW            -> STEPPER_CCW_OK
STEPPER_STOP           -> STEPPER_STOP_OK
STEPPER_RESET          -> STEPPER_RESET_OK
STEPPER_ANGLE,180      -> STEPPER_ANGLE_OK
STEPPER_SPEED,15       -> STEPPER_SPEED_OK
GET_STEPPER            -> STEPPER_STATUS:180.0,15,IDLE
```

#### Relay Commands
```
RELAY_RIGHT_ON,5       -> RELAY_RIGHT_ON_OK    (5 second timer)
RELAY_RIGHT_ON,0       -> RELAY_RIGHT_ON_OK    (Manual mode)
RELAY_RIGHT_OFF        -> RELAY_RIGHT_OFF_OK
RELAY_LEFT_ON,3        -> RELAY_LEFT_ON_OK
RELAY_LEFT_OFF         -> RELAY_LEFT_OFF_OK
GET_RELAY              -> RELAY_STATUS:1,0,4500,0,0,0
```

#### LED Matrix Commands
```
LED_MATRIX_ON          -> LED_MATRIX_ON_OK
LED_MATRIX_OFF         -> LED_MATRIX_OFF_OK
LED_MATRIX_PATTERN,1   -> LED_MATRIX_PATTERN_OK
LED_MATRIX_PATTERN,2   -> LED_MATRIX_PATTERN_OK
LED_MATRIX_BLINK       -> LED_MATRIX_BLINK_OK
```

#### System Commands
```
SAVE                   -> SETTINGS_SAVED
RESET                  -> RESET_COMPLETE
```

## 🚀 GUI Features Explained

### 1. Connection Panel
- **Port Selection**: Dropdown shows all available COM ports
- **Baud Rate**: Choose 9600, 57600, or 115200 (recommended)
- **Refresh Button**: Updates available ports list
- **Connect/Disconnect**: Establishes/closes serial connection
- **Status Indicator**: Visual connection status with animation

### 2. PWM Control Tab
- **3 Channel Control**: Red (D9), Blue (D6), Green (D5)
- **Real-time Sliders**: 0-255 value range with live preview
- **Color Visualization**: Gradient circles show intensity
- **Voltage Display**: Shows actual voltage output (0-5V)
- **Preset Buttons**: Quick access to common values
- **Live Updates**: Changes sent immediately to Arduino

### 3. Pulse Control Tab
- **Single Pulse**: One-time pulse generation
- **Continuous Mode**: Adjustable rate 1-10 Hz
- **Visual Feedback**: Animated pulse indicator
- **Rate Control**: Real-time frequency adjustment
- **Status Display**: Shows current shooting state

### 4. Stepper Motor Tab
- **Angle Control**: Precise positioning 0-359°
- **Speed Control**: 1-20 RPM adjustment
- **Manual Movement**: CW/CCW buttons
- **Position Reset**: Return to zero position
- **Status Display**: Current angle, speed, and mode

### 5. Relay Control Tab
- **Dual Relay Control**: Independent left/right operation
- **Timer Mode**: Automatic shutoff after set time
- **Manual Mode**: Continuous operation (timer = 0)
- **Status Monitoring**: Real-time relay state
- **Safety Features**: Prevents accidental activation

### 6. Test & Diagnostics Tab
- **Connection Test**: Ping Arduino for response
- **System Info**: Get Arduino firmware details
- **LED Matrix Test**: All pattern and blink tests
- **Component Tests**: Individual system testing
- **Full Diagnostic**: Comprehensive system check

### 7. Settings Tab
- **Auto Refresh**: Automatic status updates every 2 seconds
- **Manual Refresh**: On-demand status update
- **Save to Arduino**: Store settings in EEPROM
- **Reset Arduino**: Restore default values

## 🔧 Terminal Features

### Real-time Communication Log
- **Command Display**: Shows sent commands in green
- **Response Display**: Arduino responses in white
- **Error Messages**: Errors highlighted in red
- **Timestamps**: Millisecond precision timing
- **Auto-scroll**: Optional automatic scrolling
- **History**: Maintains last 1000 entries

### Terminal Controls
- **Hide/Show**: Toggle terminal visibility
- **Clear Log**: Remove all entries
- **Auto Scroll**: Enable/disable automatic scrolling
- **Fixed Position**: Always at bottom of window

## 🎨 Dark Mode Design

### Professional Appearance
- **GitHub Dark Theme**: Consistent color scheme
- **Terminal Style**: Monospace fonts for technical feel
- **High Contrast**: Excellent readability
- **Reduced Eye Strain**: Dark backgrounds
- **Modern Icons**: Professional visual elements

### Responsive Design
- **Multi-Screen Support**: Phones to desktops
- **Dynamic Sizing**: Adapts to screen dimensions
- **Mobile Optimized**: Touch-friendly on tablets
- **Scalable Interface**: Maintains usability at all sizes

## 🛠️ Testing Your Setup

### Step 1: Upload Arduino Code
1. Open Arduino IDE
2. Load `arduino_controller.ino`
3. Select your board and port
4. Upload the code
5. Open Serial Monitor (115200 baud)
6. Type "PING" - should respond "PONG"

### Step 2: Test GUI Connection
1. Run `python arduino_gui.py`
2. Select correct COM port
3. Set baud rate to 115200
4. Click "Connect"
5. Go to "Test & Diagnostics" tab
6. Click "Ping Arduino" - should show success

### Step 3: Test Components
1. **PWM Test**: Move sliders, check LED brightness
2. **LED Matrix Test**: Click "Turn ON LED Matrix"
3. **Relay Test**: Use timer mode for safety
4. **Stepper Test**: Try small angle movements first
5. **Full Diagnostic**: Run complete system test

## 🚨 Troubleshooting

### Common Issues
1. **"Port not found"**: Check USB connection and drivers
2. **"Permission denied"**: Run as administrator
3. **"No response"**: Verify baud rate and Arduino code
4. **"Connection lost"**: Check USB cable and power

### Hardware Debugging
1. **LEDs not working**: Check PWM pin connections
2. **Stepper not moving**: Verify power supply
3. **Relays not switching**: Check relay module compatibility
4. **Matrix not displaying**: Verify SPI connections

## 📊 Performance Tips

### Optimal Settings
- **Baud Rate**: Use 115200 for best performance
- **Auto Refresh**: Enable for real-time monitoring
- **Terminal**: Keep visible for debugging
- **Connection**: Use quality USB cables

### Best Practices
- **Test incrementally**: Start with simple commands
- **Monitor terminal**: Watch for error messages
- **Use diagnostics**: Regular system health checks
- **Save settings**: Store working configurations

## 🎉 Ready to Use!

Your Arduino Controller Pro is now ready for professional hardware control with:
- ✅ Real-time serial communication
- ✅ Professional dark mode interface
- ✅ Comprehensive testing capabilities
- ✅ Full hardware control
- ✅ Responsive design for all devices

**Start controlling your Arduino like a professional!** 🚀
