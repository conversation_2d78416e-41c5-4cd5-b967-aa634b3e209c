#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Terminal-Style Responsive Arduino GUI
اختبار واجهة Arduino بنمط الترمينال والاستجابة الكاملة
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import time
import threading
import random

class TerminalResponsiveTest:
    """Test terminal-style responsive Arduino interface"""
    
    def __init__(self, root):
        self.root = root
        
        # Terminal theme only
        self.current_language = "en"
        self.setup_terminal_theme()
        self.setup_translations()
        self.setup_responsive_window()
        self.create_terminal_interface()
        
        # Start demo
        self.start_terminal_demo()
    
    def setup_terminal_theme(self):
        """Setup terminal-style dark theme"""
        self.theme = {
            # Terminal-style colors
            "bg": "#0d1117",              # GitHub dark background
            "fg": "#f0f6fc",              # Bright white text
            "card_bg": "#21262d",         # Card background
            "accent": "#58a6ff",          # Blue accent
            "button_bg": "#238636",       # Green buttons
            "button_fg": "#ffffff",       # White button text
            "button_hover": "#2ea043",    # Lighter green hover
            "success": "#56d364",         # Green success
            "error": "#f85149",           # Red error
            "warning": "#e3b341",         # Yellow warning
            "info": "#79c0ff",            # Blue info
            "border": "#30363d",          # Dark border
            
            # Terminal specific colors
            "terminal_bg": "#0d1117",     # Terminal background
            "terminal_fg": "#f0f6fc",     # Terminal text
            "terminal_cursor": "#58a6ff", # Terminal cursor
            "terminal_selection": "#1f6feb", # Terminal selection
            
            # Command colors
            "cmd_input": "#56d364",       # Green for input
            "cmd_output": "#f0f6fc",      # White for output
            "cmd_error": "#f85149",       # Red for errors
            "cmd_success": "#56d364",     # Green for success
            "cmd_info": "#79c0ff",        # Blue for info
            "cmd_warning": "#e3b341",     # Yellow for warnings
            "cmd_timestamp": "#8b949e"    # Gray for timestamps
        }
    
    def setup_translations(self):
        """Setup translations"""
        self.translations = {
            "en": {
                "title": "Arduino Terminal Controller",
                "subtitle": "Terminal-Style Interface",
                "language": "Language",
                "connection": "Connection Status",
                "connected": "Connected",
                "disconnected": "Disconnected",
                "connect": "Connect",
                "disconnect": "Disconnect",
                "terminal": "Arduino Terminal",
                "send_command": "Send Command",
                "clear": "Clear"
            },
            "ar": {
                "title": "تحكم Arduino بنمط الترمينال",
                "subtitle": "واجهة بنمط الترمينال",
                "language": "اللغة",
                "connection": "حالة الاتصال",
                "connected": "متصل",
                "disconnected": "غير متصل",
                "connect": "اتصال",
                "disconnect": "قطع الاتصال",
                "terminal": "ترمينال Arduino",
                "send_command": "إرسال أمر",
                "clear": "مسح"
            }
        }
    
    def t(self, key):
        """Get translated text"""
        return self.translations[self.current_language].get(key, key)
    
    def setup_responsive_window(self):
        """Setup fully responsive window"""
        self.root.title(f"🖥️ {self.t('title')}")
        
        # Get screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # Responsive sizing
        if screen_width <= 480:  # Small phones
            width, height = int(screen_width * 0.98), int(screen_height * 0.95)
            self.is_mobile = True
        elif screen_width <= 768:  # Large phones/small tablets
            width, height = int(screen_width * 0.95), int(screen_height * 0.92)
            self.is_mobile = True
        elif screen_width <= 1024:  # Tablets/small laptops
            width, height = int(screen_width * 0.90), int(screen_height * 0.88)
            self.is_mobile = False
        else:  # Large screens
            width, height = min(1400, int(screen_width * 0.80)), min(900, int(screen_height * 0.85))
            self.is_mobile = False
        
        # Center window
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
        # Dynamic minimum size
        min_width = min(600, int(screen_width * 0.5))
        min_height = min(400, int(screen_height * 0.5))
        self.root.minsize(min_width, min_height)
        
        # Terminal styling
        self.root.configure(bg=self.theme["terminal_bg"])
        
        # Responsive grid
        self.root.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        
        # Bind resize for responsiveness
        self.root.bind('<Configure>', self.on_window_resize)
    
    def create_terminal_interface(self):
        """Create terminal-style interface"""
        # Main scrollable container
        self.main_canvas = tk.Canvas(self.root,
                                    bg=self.theme["terminal_bg"],
                                    highlightthickness=0)
        self.main_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar
        self.scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=self.main_canvas.yview)
        self.scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.main_canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # Scrollable frame
        self.scrollable_frame = tk.Frame(self.main_canvas, bg=self.theme["terminal_bg"])
        self.canvas_window = self.main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        
        # Main container
        self.main_container = tk.Frame(self.scrollable_frame,
                                      bg=self.theme["terminal_bg"],
                                      padx=20, pady=15)
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # Configure scrolling
        self.scrollable_frame.bind("<Configure>", self.on_frame_configure)
        self.main_canvas.bind("<Configure>", self.on_canvas_configure)
        self.main_canvas.bind("<MouseWheel>", self.on_mousewheel)
        
        # Create interface components
        self.create_header()
        self.create_connection_section()
        self.create_terminal_section()
        self.create_command_input()
    
    def create_header(self):
        """Create terminal-style header"""
        header_frame = tk.Frame(self.main_container, bg=self.theme["terminal_bg"])
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Title
        self.title_label = tk.Label(header_frame,
                                   text=f"🖥️ {self.t('title')}",
                                   font=('JetBrains Mono', 16, 'bold'),
                                   fg=self.theme["accent"],
                                   bg=self.theme["terminal_bg"])
        self.title_label.pack(side=tk.LEFT)
        
        # Language button
        self.language_btn = tk.Button(header_frame,
                                     text=f"🌐 {self.t('language')}",
                                     command=self.toggle_language,
                                     font=('JetBrains Mono', 9, 'bold'),
                                     bg=self.theme["button_bg"],
                                     fg=self.theme["button_fg"],
                                     activebackground=self.theme["button_hover"],
                                     relief='flat', borderwidth=1,
                                     padx=15, pady=8, cursor='hand2')
        self.language_btn.pack(side=tk.RIGHT)
    
    def create_connection_section(self):
        """Create connection section"""
        conn_frame = tk.LabelFrame(self.main_container,
                                  text=f"🔌 {self.t('connection')}",
                                  font=('JetBrains Mono', 11, 'bold'),
                                  fg=self.theme["accent"],
                                  bg=self.theme["terminal_bg"],
                                  padx=15, pady=10)
        conn_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Status
        self.status_label = tk.Label(conn_frame,
                                    text=f"🔴 {self.t('disconnected')}",
                                    font=('JetBrains Mono', 10, 'bold'),
                                    fg=self.theme["error"],
                                    bg=self.theme["terminal_bg"])
        self.status_label.pack(side=tk.LEFT)
        
        # Connect button
        self.connect_btn = tk.Button(conn_frame,
                                    text=f"🔗 {self.t('connect')}",
                                    command=self.toggle_connection,
                                    font=('JetBrains Mono', 9, 'bold'),
                                    bg=self.theme["button_bg"],
                                    fg=self.theme["button_fg"],
                                    activebackground=self.theme["button_hover"],
                                    relief='flat', borderwidth=1,
                                    padx=15, pady=6, cursor='hand2')
        self.connect_btn.pack(side=tk.RIGHT)
        
        self.is_connected = False
    
    def create_terminal_section(self):
        """Create terminal output section"""
        terminal_frame = tk.LabelFrame(self.main_container,
                                      text=f"🖥️ {self.t('terminal')}",
                                      font=('JetBrains Mono', 11, 'bold'),
                                      fg=self.theme["accent"],
                                      bg=self.theme["terminal_bg"],
                                      padx=15, pady=10)
        terminal_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # Terminal text area
        self.terminal_text = scrolledtext.ScrolledText(
            terminal_frame,
            height=15,
            bg=self.theme["terminal_bg"],
            fg=self.theme["terminal_fg"],
            insertbackground=self.theme["terminal_cursor"],
            selectbackground=self.theme["terminal_selection"],
            selectforeground=self.theme["terminal_fg"],
            font=('JetBrains Mono', 10),
            wrap=tk.NONE,
            relief='flat',
            borderwidth=2,
            highlightthickness=1,
            highlightbackground=self.theme["border"],
            highlightcolor=self.theme["accent"]
        )
        self.terminal_text.pack(fill=tk.BOTH, expand=True)
        
        # Configure terminal text tags
        self.setup_terminal_tags()
        
        # Welcome message
        self.log_terminal("Arduino Terminal Controller Started", "info")
        self.log_terminal("Type commands below and press Enter", "info")
    
    def create_command_input(self):
        """Create command input section"""
        input_frame = tk.Frame(self.main_container, bg=self.theme["terminal_bg"])
        input_frame.pack(fill=tk.X)
        
        # Command prompt
        prompt_label = tk.Label(input_frame,
                               text="$ ",
                               font=('JetBrains Mono', 12, 'bold'),
                               fg=self.theme["cmd_input"],
                               bg=self.theme["terminal_bg"])
        prompt_label.pack(side=tk.LEFT)
        
        # Command entry
        self.command_entry = tk.Entry(input_frame,
                                     font=('JetBrains Mono', 11),
                                     bg=self.theme["terminal_bg"],
                                     fg=self.theme["terminal_fg"],
                                     insertbackground=self.theme["terminal_cursor"],
                                     relief='flat',
                                     borderwidth=1,
                                     highlightthickness=1,
                                     highlightbackground=self.theme["border"])
        self.command_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 10))
        
        # Send button
        send_btn = tk.Button(input_frame,
                            text=self.t('send_command'),
                            command=self.send_command,
                            font=('JetBrains Mono', 9, 'bold'),
                            bg=self.theme["button_bg"],
                            fg=self.theme["button_fg"],
                            activebackground=self.theme["button_hover"],
                            relief='flat', borderwidth=1,
                            padx=10, pady=6, cursor='hand2')
        send_btn.pack(side=tk.RIGHT)
        
        # Clear button
        clear_btn = tk.Button(input_frame,
                             text=self.t('clear'),
                             command=self.clear_terminal,
                             font=('JetBrains Mono', 9, 'bold'),
                             bg=self.theme["warning"],
                             fg=self.theme["terminal_bg"],
                             activebackground="#f0c040",
                             relief='flat', borderwidth=1,
                             padx=10, pady=6, cursor='hand2')
        clear_btn.pack(side=tk.RIGHT, padx=(0, 10))
        
        # Bind Enter key
        self.command_entry.bind('<Return>', lambda e: self.send_command())
    
    def setup_terminal_tags(self):
        """Setup terminal text tags"""
        self.terminal_text.tag_configure("cmd_input", foreground=self.theme["cmd_input"], font=('JetBrains Mono', 10, 'bold'))
        self.terminal_text.tag_configure("cmd_output", foreground=self.theme["cmd_output"])
        self.terminal_text.tag_configure("cmd_error", foreground=self.theme["cmd_error"], font=('JetBrains Mono', 10, 'bold'))
        self.terminal_text.tag_configure("cmd_success", foreground=self.theme["cmd_success"], font=('JetBrains Mono', 10, 'bold'))
        self.terminal_text.tag_configure("cmd_info", foreground=self.theme["cmd_info"])
        self.terminal_text.tag_configure("cmd_warning", foreground=self.theme["cmd_warning"], font=('JetBrains Mono', 10, 'bold'))
        self.terminal_text.tag_configure("cmd_timestamp", foreground=self.theme["cmd_timestamp"], font=('JetBrains Mono', 9))
    
    def log_terminal(self, message, level="output"):
        """Log message to terminal"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        prefixes = {
            "input": ">>>",
            "output": "<<<",
            "error": "ERR",
            "success": "OK",
            "info": "INFO",
            "warning": "WARN"
        }
        
        prefix = prefixes.get(level, "LOG")
        
        self.terminal_text.insert(tk.END, f"[{timestamp}] ", "cmd_timestamp")
        self.terminal_text.insert(tk.END, f"{prefix}: ", f"cmd_{level}")
        self.terminal_text.insert(tk.END, f"{message}\n", f"cmd_{level}")
        self.terminal_text.see(tk.END)
    
    def send_command(self):
        """Send command from input"""
        command = self.command_entry.get().strip()
        if command:
            # Log input
            self.log_terminal(command, "input")
            
            # Simulate response
            if command.lower() == "help":
                self.log_terminal("Available commands: LED_ON, LED_OFF, PWM_SET, STATUS", "info")
            elif command.upper() == "LED_ON":
                self.log_terminal("LED turned ON", "success")
            elif command.upper() == "LED_OFF":
                self.log_terminal("LED turned OFF", "success")
            elif command.upper().startswith("PWM_SET"):
                self.log_terminal(f"PWM set: {command}", "success")
            elif command.upper() == "STATUS":
                self.log_terminal("System status: OK", "info")
            elif command.upper() == "ERROR":
                self.log_terminal("Command failed: Invalid parameter", "error")
            else:
                self.log_terminal(f"Unknown command: {command}", "warning")
            
            # Clear input
            self.command_entry.delete(0, tk.END)
    
    def clear_terminal(self):
        """Clear terminal"""
        self.terminal_text.delete(1.0, tk.END)
        self.log_terminal("Terminal cleared", "info")
    
    def toggle_connection(self):
        """Toggle connection state"""
        self.is_connected = not self.is_connected
        if self.is_connected:
            self.status_label.config(text=f"🟢 {self.t('connected')}", fg=self.theme["success"])
            self.connect_btn.config(text=f"🔌 {self.t('disconnect')}")
            self.log_terminal("Connected to Arduino", "success")
        else:
            self.status_label.config(text=f"🔴 {self.t('disconnected')}", fg=self.theme["error"])
            self.connect_btn.config(text=f"🔗 {self.t('connect')}")
            self.log_terminal("Disconnected from Arduino", "warning")
    
    def toggle_language(self):
        """Toggle language"""
        self.current_language = "ar" if self.current_language == "en" else "en"
        self.refresh_ui()
    
    def refresh_ui(self):
        """Refresh UI with new language"""
        self.root.title(f"🖥️ {self.t('title')}")
        self.title_label.config(text=f"🖥️ {self.t('title')}")
        self.language_btn.config(text=f"🌐 {self.t('language')}")
        
        status_text = self.t('connected') if self.is_connected else self.t('disconnected')
        self.status_label.config(text=f"{'🟢' if self.is_connected else '🔴'} {status_text}")
        
        connect_text = self.t('disconnect') if self.is_connected else self.t('connect')
        self.connect_btn.config(text=f"{'🔌' if self.is_connected else '🔗'} {connect_text}")
    
    def on_frame_configure(self, event):
        """Configure scroll region"""
        self.main_canvas.configure(scrollregion=self.main_canvas.bbox("all"))
    
    def on_canvas_configure(self, event):
        """Configure canvas window width"""
        canvas_width = event.width
        self.main_canvas.itemconfig(self.canvas_window, width=canvas_width)
    
    def on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        self.main_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def on_window_resize(self, event):
        """Handle window resize"""
        if event.widget == self.root:
            # Update responsive layout based on new size
            width = event.width
            height = event.height
            
            # Adjust font sizes for mobile
            if width < 600:
                font_size = 8
            elif width < 900:
                font_size = 9
            else:
                font_size = 10
            
            # Update terminal font size
            if hasattr(self, 'terminal_text'):
                self.terminal_text.config(font=('JetBrains Mono', font_size))
    
    def start_terminal_demo(self):
        """Start terminal demo"""
        def demo_commands():
            import time
            time.sleep(2)
            
            demo_cmds = [
                ("STATUS", "System initialized"),
                ("LED_ON", "LED activated"),
                ("PWM_SET,255", "PWM set to maximum"),
                ("SENSOR_READ", "Temperature: 25.3°C"),
            ]
            
            for cmd, response in demo_cmds:
                if hasattr(self, 'terminal_text'):
                    self.root.after(0, lambda c=cmd: self.log_terminal(c, "input"))
                    time.sleep(0.5)
                    self.root.after(0, lambda r=response: self.log_terminal(r, "success"))
                    time.sleep(2)
        
        threading.Thread(target=demo_commands, daemon=True).start()

def main():
    """Main function"""
    root = tk.Tk()
    app = TerminalResponsiveTest(root)
    root.mainloop()

if __name__ == "__main__":
    main()
